{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Out - JPR Dry Fish </title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            position: relative;
            overflow-x: hidden;
        }
        
        /* Animated background elements */
        .bg-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-shapes:nth-child(1) {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
            width: 80px;
            height: 80px;
        }
        
        .floating-shapes:nth-child(2) {
            top: 25%;
            right: 15%;
            animation-delay: 2s;
            width: 120px;
            height: 120px;
        }
        
        .floating-shapes:nth-child(3) {
            bottom: 25%;
            left: 20%;
            animation-delay: 4s;
            width: 60px;
            height: 60px;
        }
        
        .floating-shapes:nth-child(4) {
            bottom: 15%;
            right: 10%;
            animation-delay: 1s;
            width: 90px;
            height: 90px;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .logout-container {
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .logout-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            padding: 3.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .logout-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 35px 70px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }
        
        .logout-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
        }
        
        .logout-icon i {
            font-size: 3rem;
            color: white;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .logout-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #2c3e50 0%, #ff6b6b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .logout-subtitle {
            color: #64748b;
            font-size: 1.2rem;
            font-weight: 400;
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }
        
        .user-info {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .user-details h4 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .user-details p {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .logout-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .btn-logout {
            flex: 1;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 1.3rem;
            border: none;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
        }
        
        .btn-logout::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-logout:hover::before {
            left: 100%;
        }
        
        .btn-logout:hover {
            background: linear-gradient(135deg, #e55656 0%, #d63031 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }
        
        .btn-cancel {
            flex: 1;
            background: rgba(255, 255, 255, 0.8);
            color: #374151;
            padding: 1.3rem;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-cancel:hover {
            background: white;
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .back-home {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.8rem 1.2rem;
            border-radius: 50px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .back-home:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }
        
        .security-note {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 2rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-size: 0.9rem;
            color: #92400e;
        }
        
        .security-note i {
            color: #f59e0b;
            font-size: 1.1rem;
        }
        
        @media (max-width: 768px) {
            .logout-card {
                padding: 2.5rem;
                margin: 1rem;
                border-radius: 20px;
            }
            
            .logout-title {
                font-size: 2rem;
            }
            
            .logout-subtitle {
                font-size: 1.1rem;
            }
            
            .logout-actions {
                flex-direction: column;
            }
            
            .back-home {
                top: 1rem;
                left: 1rem;
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }
            
            .floating-shapes {
                display: none;
            }
        }
        
        @media (max-width: 480px) {
            .logout-card {
                padding: 2rem;
            }
            
            .logout-title {
                font-size: 1.8rem;
            }
            
            .logout-icon {
                width: 80px;
                height: 80px;
            }
            
            .logout-icon i {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-shapes"></div>
        <div class="floating-shapes"></div>
        <div class="floating-shapes"></div>
        <div class="floating-shapes"></div>
    </div>

    <a href="/" class="back-home">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>

    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-icon">
                <i class="fas fa-sign-out-alt"></i>
            </div>
            
            <h1 class="logout-title">Sign Out</h1>
            <p class="logout-subtitle">Are you sure you want to sign out of your account?</p>
            
            {% if user.is_authenticated %}
            <div class="user-info">
                <div class="user-avatar">
                    {{ user.username|first|upper }}
                </div>
                <div class="user-details">
                    <h4>{{ user.username }}</h4>
                    <p>Currently signed in</p>
                </div>
            </div>
            {% endif %}
            
            <form method="post" style="margin: 0;">
                {% csrf_token %}
                <div class="logout-actions">
                    <button type="submit" class="btn-logout">
                        <i class="fas fa-sign-out-alt"></i> Yes, Sign Out
                    </button>
                    <a href="/" class="btn-cancel">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
            
            <div class="security-note">
                <i class="fas fa-shield-alt"></i>
                <span>For your security, you'll be signed out from all devices.</span>
            </div>
        </div>
    </div>
</body>
</html>
