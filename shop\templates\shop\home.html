{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JPR Dry Fish - Fresh & Quality Dry Fish</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }
        .product-card:hover .btn {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> RJP Dry Fish
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                    <li><a href="/accounts/signup/">Sign Up</a></li>
                {% endif %}
                <li><a href="#contact">Contact</a></li>
            </ul>
            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </button>
            <div class="mobile-menu" id="mobileMenu">
                <a href="/">Home</a>
                <a href="/products/">Products</a>
                {% if user.is_authenticated %}
                    <a href="/orders/">Orders</a>
                    <a href="/accounts/logout/">Logout</a>
                {% else %}
                    <a href="/accounts/login/">Login</a>
                    <a href="/accounts/signup/">Sign Up</a>
                {% endif %}
                <a href="#contact">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1 class="fade-in-up">Welcome to RJP Dry Fish</h1>
            <p class="fade-in-up">Fresh, Quality Dry Fish from sea to Your Kitchen</p>
            {% if user.is_authenticated %}
                <p class="fade-in-up">Welcome back, {{ user.first_name|default:user.email|default:user.username }}!</p>
            {% endif %}
            <a href="/products/" class="btn btn-primary fade-in-up">View Products</a>
        </div>
    </section>

    <!-- Messages -->
    {% if messages %}
        <div class="container">
            <div class="messages">
                {% for message in messages %}
                    <div class="message success">
                        <i class="fas fa-check-circle"></i> {{ message }}
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- 5-Star Reviews Section -->
    {% if five_star_reviews %}
    <section class="container" style="margin: 4rem auto; padding: 0 1rem;">
        <div class="text-center mb-4">
            <h2 style="font-size: 2.5rem; color: #1f2937; margin-bottom: 1rem;">
                <i class="fas fa-star" style="color: #ffc107;"></i> Customer Reviews
            </h2>
            <p style="color: #6b7280; font-size: 1.1rem;">What our customers say about our products</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 2rem;">
            {% for product_name, product_data in five_star_reviews.items %}
                <div style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                    <h3 style="color: #1f2937; margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-fish" style="color: #059669;"></i>
                        {{ product_data.display_name }}
                    </h3>

                    {% for review in product_data.reviews %}
                        <div style="background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; border-left: 3px solid #ffc107;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <i class="fas fa-user-circle" style="font-size: 1.5rem; color: #6b7280;"></i>
                                    <strong style="color: #1f2937;">{{ review.user.first_name|default:review.user.username }}</strong>
                                </div>
                                <div style="color: #ffc107; font-size: 1.2rem;">
                                    {{ review.get_star_display }}
                                </div>
                            </div>
                            {% if review.review_text %}
                                <p style="color: #4b5563; line-height: 1.6; margin: 0; font-style: italic;">
                                    "{{ review.review_text }}"
                                </p>
                            {% endif %}
                            <div style="color: #9ca3af; font-size: 0.875rem; margin-top: 0.5rem;">
                                {{ review.created_at|date:"M d, Y" }}
                            </div>
                        </div>
                    {% endfor %}

                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="{% url 'product_reviews' product_name %}" style="color: #059669; text-decoration: none; font-weight: 500;">
                            <i class="fas fa-eye"></i> View All Reviews
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- Products Section -->
    <section id="products" class="container">
        <div class="text-center mb-4">
            <h2 style="font-size: 2.5rem; color: #1f2937; margin-bottom: 1rem;">Our Premium Products</h2>
            <p style="color: #6b7280; font-size: 1.1rem;">Handpicked and carefully dried for the best taste</p>
        </div>

        <div class="product-grid">
            {% for product in products %}
            <a href="{% url 'product_detail' product %}" class="product-card fade-in-up" style="text-decoration: none; color: inherit; display: block; transition: transform 0.3s ease, box-shadow 0.3s ease;">
                <img src="{% static 'shop/images/' %}{{ product|slugify }}.jpg" alt="{{ product }}">
                <div class="product-info">
                    <h2>{{ product|title }}</h2>
                    <div class="product-description">
                        {% if product == "motha kendai" %}
                            <p>Traditional flavor, perfect for spicy South Indian curries and authentic coastal recipes.</p>
                        {% elif product == "netthili" %}
                            <p>Small, crispy dry fish perfect for frying and side dishes, loved across Tamil Nadu.</p>
                        {% elif product == "vaalai" %}
                            <p>Soft texture and rich taste, ideal for gravies and mixed rice preparations.</p>
                        {% elif product == "goa netthili" %}
                            <p>Coastal flavors with a slightly larger size and saltier taste than regular Netthili.</p>
                        {% elif product == "yeera" %}
                            <p>Intense umami flavor perfect for chutneys, stir-fries, and traditional seafood recipes.</p>
                        {% endif %}
                    </div>
                    <div class="btn btn-primary" style="margin-top: 1rem; display: inline-block; pointer-events: none;">
                        <i class="fas fa-eye"></i> View Details
                    </div>
                </div>
            </a>
            {% endfor %}
        </div>
    </section>

    <!-- Call to Action -->
    <section class="container text-center" style="padding: 3rem 0;">
        <h2 style="color: #1f2937; margin-bottom: 1rem;">Ready to Order?</h2>
        <p style="color: #6b7280; margin-bottom: 2rem;">Get fresh, quality dry fish delivered to your doorstep</p>
        <a href="/order/" class="order-btn">
            <i class="fas fa-shopping-cart"></i> Place Your Order
        </a>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-fish"></i> JP Dry Fish</h3>
                    <p>Your trusted source for premium quality dry fish. Fresh from Gudiyattam, delivered with care.</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-map-marker-alt"></i> Gudiyattam, Tamil Nadu</p>
                    <p><i class="fas fa-phone"></i> +91 6369477095</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <div style="margin-top: 1rem;">
                        <a href="https://wa.me/************?text=Hi! I'm interested in JP Dry Fish products from Gudiyattam."
                           style="background: #25d366; color: white; padding: 0.8rem 1.5rem; border-radius: 25px; text-decoration: none; display: inline-flex; align-items: center; gap: 0.5rem; font-weight: 600; transition: all 0.3s ease;"
                           target="_blank"
                           onmouseover="this.style.background='#128c7e'"
                           onmouseout="this.style.background='#25d366'">
                            <i class="fab fa-whatsapp"></i> WhatsApp Us
                        </a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <p><a href="/">Home</a></p>
                    <p><a href="/order/">Order</a></p>
                    <p><a href="/accounts/login/">Login</a></p>
                    <p><a href="/accounts/signup/">Sign Up</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 JP Dry Fish. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }

        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
