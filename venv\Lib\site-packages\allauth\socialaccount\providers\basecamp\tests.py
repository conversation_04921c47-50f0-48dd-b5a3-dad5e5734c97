from django.test import TestCase

from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse

from .provider import BasecampProvider


class BasecampTests(OAuth2TestsMixin, TestCase):
    provider_id = BasecampProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
        {
            "expires_at": "2012-03-22T16:56:48-05:00",
            "identity": {
                "id": 9999999,
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "email_address": "<EMAIL>"
            },
            "accounts": [
                {
                    "product": "bcx",
                    "id": ********,
                    "name": "Wayne Enterprises, Ltd.",
                    "href": "https://basecamp.com/********/api/v1"
                },
                {
                    "product": "bcx",
                    "id": ********,
                    "name": "Veidt, Inc",
                    "href": "https://basecamp.com/********/api/v1"
                },
                {
                    "product": "campfire",
                    "id": ********,
                    "name": "Acme Shipping Co.",
                    "href": "https://acme4444444.campfirenow.com"
                }
            ]
        }""",
        )

    def get_expected_to_str(self):
        return "<EMAIL>"
