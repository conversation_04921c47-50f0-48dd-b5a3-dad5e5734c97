# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-03-12 17:52+0000\n"
"Last-Translator: \"Xaver <PERSON><PERSON><PERSON><PERSON>\" <<EMAIL>>\n"
"Language-Team: Chinese (Traditional Han script) <https://hosted.weblate.org/"
"projects/allauth/django-allauth/zh_Hant/>\n"
"Language: zh_Hant\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.10.3-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "此帳號目前沒有啟用。"

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "您不能移除您的主要電子郵件地址。"

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "此電子郵件地址已經與此帳號連結了。"

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "您提供的電子郵件地址和／或密碼不正確。"

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "您提供的電話號碼和／或密碼不正確。"

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "已經有使用者使用此電子郵件地址註冊。"

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "請輸入您目前的密碼。"

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "不正確的代碼。"

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "不正確的密碼。"

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "無效或已過期的金鑰。"

#: account/adapter.py:73
msgid "Invalid login."
msgstr "登入無效。"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "密碼重置令牌無效。"

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "您不能新增超過 %d 個電子郵件地址。"

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "已經有使用者使用此電子郵件地址註冊。"

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "登入嘗試失敗次數過多。請稍後再試。"

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "此電子郵件地址未分配給任何使用者帳號"

#: account/adapter.py:81
#, fuzzy
#| msgid "The phone number is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "此電話未分配給任何使用者帳號"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "您的主要電子郵件地址必須已驗證。"

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "無法使用此使用者名稱。請使用其他使用者名稱。"

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "您提供的使用者名稱和／或密碼不正確。"

#: account/adapter.py:92
msgid "Please select only one."
msgstr "請僅選擇一項。"

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "新的輸入值必須不同於目前的內容。"

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "使用您的密碼"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "使用驗證器應用程式或代碼"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "使用安全金鑰"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "標記已選取的電子郵件地址為已驗證"

#: account/apps.py:11
msgid "Accounts"
msgstr "帳號"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr "請輸入包含國家碼的電話號碼 (例如：+1 美國)。"

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "電話"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "您必須每次輸入相同的密碼。"

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "密碼"

#: account/forms.py:100
msgid "Remember Me"
msgstr "記住我"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "電子郵件地址"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "電子郵件"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "使用者名稱"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "登入"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "使用者名稱或電子郵件"

#: account/forms.py:156
msgid "Username or email"
msgstr "使用者名稱或電子郵件"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "使用者名稱或電子郵件"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "電子郵件（選擇性）"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "忘記您的密碼？"

#: account/forms.py:334
msgid "Email (again)"
msgstr "電子郵件（再次）"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "電子郵件地址確認"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "電子郵件（選擇性）"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "使用者名稱（選填）"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "您必須每次輸入相同的電子郵件。"

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "密碼（再次）"

#: account/forms.py:645
msgid "Current Password"
msgstr "目前密碼"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "新密碼"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "新密碼（再次）"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "代碼"

#: account/models.py:26
msgid "user"
msgstr "使用者"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "電子郵件地址"

#: account/models.py:34
msgid "verified"
msgstr "已驗證"

#: account/models.py:35
msgid "primary"
msgstr "主要"

#: account/models.py:41
msgid "email addresses"
msgstr "電子郵件地址"

#: account/models.py:151
msgid "created"
msgstr "已建立"

#: account/models.py:152
msgid "sent"
msgstr "已傳送"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "金鑰"

#: account/models.py:158
msgid "email confirmation"
msgstr "電子郵件確認"

#: account/models.py:159
msgid "email confirmations"
msgstr "電子郵件確認"

#: headless/apps.py:7
msgid "Headless"
msgstr "無頭"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr "您不能將電子郵件地址新增至受雙重驗證保護的帳號。"

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "您不能停用雙重驗證。"

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "您不能在未啟用雙重驗證下產生復原代碼。"

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr "您不能在驗證您的電子郵件地址之前啟用雙重驗證。"

#: mfa/adapter.py:141
msgid "Master key"
msgstr "主要金鑰"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "備份金鑰"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "鑰匙編號 {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "多重要素驗證"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "恢復代碼"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP 驗證器"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "網頁身份驗證"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "驗證器代碼"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "無密碼"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"啟用無密碼操作後，您可以僅使用此金鑰登入，但會要求額外的保護措施，如生物識別"
"或 PIN 碼保護。"

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"已經有一個帳號與此電子郵件連結了，請先登入該帳號，然後連接你的 %s 帳號。"

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "無效的令牌。"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "您的帳號沒有設置密碼。"

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "您的帳號下沒有驗證過的電子郵件地址。"

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "您無法斷開您最後一個的第三方帳戶。"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "該第三方帳號已連結至其他帳戶。"

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "社群帳號"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "提供者"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "第三方提供者 ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "名稱"

#: socialaccount/models.py:58
msgid "client id"
msgstr "client id"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "應用程式 ID 或消費者金鑰"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "秘密金鑰"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API 秘密、客戶秘密或消費者秘密"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "金鑰"

#: socialaccount/models.py:81
msgid "social application"
msgstr "社群應用程式"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "社群應用程式"

#: socialaccount/models.py:117
msgid "uid"
msgstr "使用者 ID"

#: socialaccount/models.py:119
msgid "last login"
msgstr "最後一次登入"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "加入日期"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "額外資料"

#: socialaccount/models.py:125
msgid "social account"
msgstr "社群帳號"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "社群帳號"

#: socialaccount/models.py:160
msgid "token"
msgstr "令牌"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "「oauth_token」（OAuth1）或存取 token（OAuth2）"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token 密鑰"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "「oauth_token_secret」（OAuth1）或刷新 token（OAuth2）"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "過期日"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "社群應用程式 Token"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "社群應用程式 Token"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "無效的資料"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "登入"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "取消"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "從 \"%s\" 獲取請求令牌時回應無效。回應內容為：%s。"

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "從 \"%s\" 獲取 access token 時回應無效。"

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "未為 \"%s\" 儲存請求 token。"

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "未為 \"%s\" 儲存 access token。"

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "無權訪問私有資源 \"%s\"。"

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "從 \"%s\" 獲取 request token 時回應無效。"

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "帳號未啟用"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "此帳號未啟用。"

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr "我們已經代碼傳送給%(recipient)s．此代碼有效期限極短，請盡速輸入。"

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "確認"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "請求代碼"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "確認存取權限"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "請重新認證以保護您的帳號。"

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "替代選項"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "電子郵件驗證"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "輸入電子郵件驗證碼"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "電子郵件地址"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "登入"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "輸入登入碼"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "密碼重設"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "輸入密碼重設代碼"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "電話驗證"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "輸入電話驗證代碼"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "電子郵件地址"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "下列電子郵件已與您的帳號連結："

#: templates/account/email.html:25
msgid "Verified"
msgstr "已驗證"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "未驗證"

#: templates/account/email.html:34
msgid "Primary"
msgstr "主要的"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "設為主要的"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "重寄驗証信"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "移除"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "增加電子郵件"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "增加電子郵件"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "您真的要移除所選擇電子郵件嗎？"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"您收到此電子郵件是因為您(或其他人)嘗試使用以下電子郵件地址\n"
"註冊帳戶：\n"
"\n"
"%(email)s\n"
"\n"
"然而，目前已經有使用該電子郵件地址的帳戶存在。\n"
"\n"
"如果您忘記了帳戶，請使用忘記密碼程序來找回您的帳戶：\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "帳號已存在"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "來自%(site_name)s!的問候！"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"感謝您使用%(site_name)s。\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "您收到此封郵件是因為您的帳號已做出以下變更："

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"如果您沒有進行此變更，請立即採取適當的安全措施。\n"
"此變更來源：\n"
"- IP 位址：%(ip)s\n"
"- 瀏覽器：%(user_agent)s\n"
"- 日期：%(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "您的 Email 已從 %(from_email)s 修改成 %(to_email)s。"

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "電子郵件已更改"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "您的電子郵件已確認。"

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "電子郵件確認"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"您會收到此 Email 是因為使用者 %(user_display)s 提供此 Email 於網"
"站%(site_domain)s 上註冊帳號。"

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr "您的 Email 驗證代碼如下所列，請在您已開啟的瀏覽器視窗中輸入。"

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "若要確認此操作正確，請前往 %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "確認您的電子郵件"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "電子郵件地址 %(deleted_email)s 已從您的帳戶中移除。"

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Email 已移除"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr "您的登入代碼如下所列，請在您已開啟的瀏覽器視窗中輸入。"

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "若確定非您所為，請忽略此郵件。"

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "登入代碼"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "您的密碼已變更。"

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "密碼已更改"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr "您的密碼重設代碼如下所列，請在您已開啟的瀏覽器視窗中輸入。"

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "密碼重設代碼"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"您收到此電子郵件是因為您或其他人已請求重設您的帳戶密碼。\n"
"如果您沒有請求重設密碼，則可以安全忽略此郵件。請點擊以下連結來重設您的密碼。"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "提醒您，您的使用者名稱是 %(username)s 。"

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "密碼重設電子郵件"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "您的密碼已被重設。"

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "您的密碼已設定完成。"

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "密碼已設定"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"您會收到此封 Email 是因為您/或是有人嘗試使用 %(email)s 此 Email 存取帳號。然"
"而，我們於資料庫中找不到任何關聯的帳號紀錄。"

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "如果確定是您，您可以透過下列連結註冊一個帳號。"

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "未知帳戶"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "電子郵件地址"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "目前電子郵件"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "變更為"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "您的電子郵件地址仍在等待驗證。"

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "取消變更"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "變更為"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "更改電子郵件"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "確認電子郵件"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"請確認 <a href=\"mailto:%(email)s\">%(email)s</a> 是使用者 %(user_display)s "
"所使用的電子郵件地址。"

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "無法驗證 %(email)s，因為該電子郵件已由其他帳戶驗證。"

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"此電子郵件確認連結已過期或無效。請<a href=\"%(email_url)s\">重新發送電子郵件"
"確認請求</a>。"

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "若您沒有帳號，請先 %(link)s註冊%(end_link)s 。"

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "使用通行密鑰登入"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "寄給我登入代碼"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "登出"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "您確定要登出嗎？"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "您不能移除您的主要的電子郵件地址 (%(email)s) 。"

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "確認信已發至 %(email)s 。"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "您已確認 %(email)s。"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "電子郵件地址 %(email)s 已刪除。"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "成功以 %(name)s..的身份登入。"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "您已登出。"

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "登入代碼已經寄給 %(recipient)s。"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "密碼修改完成。"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "密碼設定完成。"

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "登入代碼已經寄給 %(recipient)s。"

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "已設定好主要的電子郵件地址。"

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "修改密碼"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "忘記密碼了？"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"忘記您的密碼了嗎？ 請在下方輸入您的電子郵件，我們會發送一封電子郵件給您，以便"
"重新設定您的密碼。"

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "重設我的密碼"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "如果在重設密碼時碰到問題，請與我們聯絡。"

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"我們已經寄送了一封電子郵件給您。如果您沒有收到，請檢查您的垃圾郵件資料夾。如"
"果仍未收到，請在幾分鐘後聯繫我們。"

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "無效的 token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"密碼重設連結已失效，可能是因為該連結已經被人用過了，請重新申請<a "
"href=\"%(passwd_reset_url)s\">重設密碼</a>。"

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "您的密碼已變更。"

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "設定密碼"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "更改電話號碼"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "目前電話號碼"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "您的電子郵件地址仍在等待驗證。"

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "輸入您的密碼："

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "您將會收到一組特殊代碼用於免密碼登入。"

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "請求代碼"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "其他登入選項"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "註冊"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "註冊"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "已有帳號了嗎？請%(link)s登入%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "使用通行密鑰註冊"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "使用通行密鑰註冊"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "其他選項"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "註冊未開放"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "很抱歉，目前不開放註冊。"

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "注意"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "您已經以 %(user_display)s 的身份登入了。"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "警告："

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"您尚未設定任何電子郵件。建議您最好設定一個電子郵件，以便您接收通知或重新設定"
"密碼等等。"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "驗證您的電子郵件地址"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"我們已經向您發送了一封電子郵件進行驗證。請按照郵件中的連結完成註冊過程。如果"
"您在主要收件匣中沒有看到驗證郵件，請檢查您的垃圾郵件資料夾。如果幾分鐘後仍未"
"收到驗證郵件，請與我們聯繫。"

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"此網站的此部分要求我們驗證您的身份。\n"
"為此，\n"
"我們要求您驗證您的電子郵件地址的所有權。 "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"我們已經向您發送了一封電子郵件進行驗證。\n"
"請點擊郵件中的連結。如果您在主要收件匣中沒有看到驗證郵件，請檢查您的垃圾郵件"
"資料夾。\n"
"如果幾分鐘後仍未收到，請與我們聯繫。"

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>注意：</strong> 您仍能<a href=\"%(email_url)s\">修改您的電子郵件地址 "
"</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "訊息："

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "選單："

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "帳號連結"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "二階段認證"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "登入階段"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr "您的帳號由二階驗證所保護，請輸入驗證器中的代碼："

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "新的二階驗證復原代碼已建立。"

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "新的復原代碼已建立"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "驗證器已啟用。"

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "驗證器已啟用"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "驗證器已停用。"

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "驗證器已停用"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "新的安全金鑰已成功新增。"

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "已新增安全金鑰"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "安全金鑰已被移除。"

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "安全金鑰已移除"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "驗證器"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "已啟用驗證器進行驗證。"

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "authenticator 驗證器應用程式未啟用。"

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "停用"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "啟用"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "安全金鑰"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "您已新增 %(count)s 個安全金鑰。"

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "尚未新增任何安全金鑰。"

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "管理"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "新增"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "復原碼"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "共有 %(total_count)s 個恢復碼，其中剩餘 %(unused_count)s 個可用。"

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "尚未設定恢復碼。"

#: templates/mfa/index.html:96
msgid "View"
msgstr "檢視"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "下載"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "產生"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "已產生一組新的恢復碼。"

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "已成功新增安全金鑰。"

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "安全金鑰已成功移除。"

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "輸入驗證器代碼："

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "您即將為您的帳戶產生一組新的恢復碼。"

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "此操作將使您現有的恢復碼失效。"

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "您確定嗎？"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "未使用之代碼"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "下載代碼"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "產生新的代碼"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "啟用驗證器"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"為了使用雙重驗證保護您的帳戶，請使用您的驗證器應用程式掃描下方的 QR 碼，然後"
"輸入應用程式產生的驗證碼。"

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Authenticator 驗證器密鑰"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr "您可以儲存此密鑰，稍後重新安裝 authenticator 驗證器應用程式時使用。"

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "停用驗證器"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "您即將停用基於 authenticator 驗證器應用程式的身份驗證。確定要繼續嗎？"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "新增安全金鑰"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "移除安全金鑰"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "您確定要移除此安全金鑰嗎？"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "使用方式"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "通行密鑰"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "安全金鑰"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "此金鑰無法判斷是否為通行密鑰（Passkey）。"

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "未指定"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "新增於 %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "上次使用時間：%(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "編輯"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "編輯安全金鑰"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "儲存"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "建立通行密鑰"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"您即將為您的帳戶建立通行密鑰（Passkey）。由於您可以稍後新增其他金鑰，建議使用"
"具描述性的名稱來區分不同金鑰。"

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "建立"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "此功能需要JavaScript支援。"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "第三方帳號登入失敗"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "當嘗試用您的第三方網路帳號登入時發生錯誤。"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "您可以使用下列任何第三方帳號登入您的帳號："

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "您目前沒有任何帳號與此帳號連結。"

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "增加一個第三方帳號"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "來自 %(provider)s 的第三方帳戶已連結至您的帳戶。"

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "第三方帳號已關聯"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "來自 %(provider)s 的第三方帳戶已移除與您的帳戶的連結。"

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "第三方帳號已取消關聯"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "連接 %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "您即將連結來自 %(provider)s 的新第三方帳戶。"

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "透過 %(provider)s 登入"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "您即將使用來自 %(provider)s 的第三方帳戶登入。"

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "繼續"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "登入取消了"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"您決定不繼續登入這一個網站。若這是一個失誤，請<a href=\"%(login_url)s\">由此"
"</a>重新登入。"

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "第三方帳號已連結。"

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "第三方帳號連結已中斷。"

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"您即將使用您的 %(provider_name)s 帳戶登入\n"
"%(site_name)s。作為最後一步，請完成以下表單："

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "或是使用第三方"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "登出所有其他登入階段。"

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "開始於"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP 地址"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "瀏覽器"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "最後出現於"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "目前"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "登出其他使用階段"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "使用者會話"

#: usersessions/models.py:92
msgid "session key"
msgstr "會話金鑰"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "帳號連結"

#~ msgid "Use security key or device"
#~ msgstr "使用安全金鑰或裝置"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "密碼長度至少要有 {0} 個字元。"

#, fuzzy, python-format
#~| msgid ""
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account at %(site_domain)s.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "您會收到這封信是因為您或是某人在 %(site_domain)s 這個網站上要求重設您帳號"
#~ "的密碼。\n"
#~ "若您沒有要求我們重設密碼，請您直接忽略這封信。若要重設您的密碼，請點擊下面"
#~ "的連結。"

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "下列電子郵件已與你的帳號連結："

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "確認電子郵件"

#, fuzzy, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "請用您的第三方帳號登入。\n"
#~ "或者%(link)s註冊%(end_link)s \n"
#~ "一個 %(site_name)s帳號後登入："

#~ msgid "or"
#~ msgstr "或"

#~ msgid "change password"
#~ msgstr "修改密碼"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID 登入"

#~ msgid "This email address is already associated with another account."
#~ msgstr "此電子郵件已經與別的帳號連結了。"

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "我們已經寄了一封電子郵件給您，如果數分鐘內您沒有收到，請與我們聯絡。"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "您提供的帳號或密碼不正確。"

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "使用者名稱只能包含字母，數字及 @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "這個使用者名稱已經有人用了，請換一個。"

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "登入"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "您以確認<a href=\"mailto:%(email)s\">%(email)s</a>是使用者%(user_display)s"
#~ "的電子郵件地址。"

#~ msgid "Thanks for using our site!"
#~ msgstr "感謝您使用我們的網站！"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "确认e-mail已发往 %(email)s"

#~ msgid "Delete Password"
#~ msgstr "删除密码"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "您当前使用OpenID登录，因此您可以删除你的密码。"

#~ msgid "delete my password"
#~ msgstr "删除我的密码"

#~ msgid "Password Deleted"
#~ msgstr "密码已删除"

#~ msgid ""
#~ "If you have any trouble resetting your password, contact us at <a "
#~ "href=\"mailto:%(CONTACT_EMAIL)s\">%(CONTACT_EMAIL)s</a>."
#~ msgstr ""
#~ "Als je problemen hebt je wachtwoord opnieuw in te stellen, neem dan "
#~ "contact op met <a href=\"mailto:%(CONTACT_EMAIL)s\">%(CONTACT_EMAIL)s</a>."

#~ msgid "OpenID"
#~ msgstr "OpenID"

#~ msgid "Already have an account?"
#~ msgstr "Heb je al een account?"

#~ msgid "Sign in"
#~ msgstr "Aanmelden"

#~ msgid "Language"
#~ msgstr "Taal"

#~ msgid "Pinax can be used in your preferred language."
#~ msgstr "Deze site kan in jouw voorkeurstaal gebruikt worden."

#~ msgid "Change my language"
#~ msgstr "Verander mijn taal"

#~ msgid "Timezone"
#~ msgstr "Tijdzone"

#, fuzzy
#~ msgid ""
#~ "You're receiving this e-mail because you requested a password reset\n"
#~ "for your user account at Pinax.\n"
#~ "\n"
#~ "Your new password is: %(new_password)s\n"
#~ "\n"
#~ "Your username, in case you've forgotten: %(username)s\n"
#~ "\n"
#~ "You should log in as soon as possible and change your password.\n"
#~ "\n"
#~ "Thanks for using our site!\n"
#~ msgstr ""
#~ "Je ontvangt deze mail omdat er een verzoek is ingelegd om het wachtwoord\n"
#~ "behorende bij je %(site_name)s account opnieuw in te stellen.\n"
#~ "\n"
#~ "Je nieuwe wachtwoord is: %(new_password)s\n"
#~ "\n"
#~ "Je gebruikersnaam, voor het geval je die vergeten bent, is: %(username)s\n"
#~ "\n"
#~ "Je moet zo snel mogelijk inloggen en bovenstaand wachtwoord veranderen.\n"
#~ "\n"
#~ "Bedankt voor het gebruik van onze site!\n"

#~ msgid "If checked you will stay logged in for 3 weeks"
#~ msgstr "Bij 'Onthouden' blijf je ingelogd gedurende 3 weken"

#~ msgid "Timezone successfully updated."
#~ msgstr "Tijdzone gewijzigd."

#~ msgid "Language successfully updated."
#~ msgstr "Taal gewijzigd."

#~ msgid "None"
#~ msgstr "Geen"

#~ msgid "Log In"
#~ msgstr "Inloggen"

#~ msgid "Log in"
#~ msgstr "Inloggen"

#~ msgid "Logout"
#~ msgstr "Afmelden"

#~ msgid ""
#~ "When you receive the new password, you should <a "
#~ "href=\"%(login_url)s\">log in</a> and change it as soon as possible."
#~ msgstr ""
#~ "Zodra je het nieuwe wachtwoord ontvangen hebt moet je zo snel mogelijk <a "
#~ "href=\"%(login_url)s\">inloggen</a> en het wachtwoord wijzigen."

#~ msgid "You are already logged in."
#~ msgstr "Je bent al ingelogd."

#~ msgid ""
#~ "By clicking \"Sign Up\", you are indicating that you have read and agree "
#~ "to the <a href=\"%(terms_url)s\">Terms of Use</a> and <a "
#~ "href=\"%(privacy_url)s\">Privacy Policy</a>."
#~ msgstr ""
#~ "Door te registreren geef je aan dat je de <a "
#~ "href=\"%(terms_url)s\">gebruiksvoorwaarden</a> en de <a "
#~ "href=\"%(privacy_url)s\">privacy policy</a> gelezen hebt en ermee akkoord "
#~ "gaat."

#~ msgid ""
#~ "If you have any trouble creating your account, contact us at <a "
#~ "href=\"mailto:%(contact_email)s\">%(contact_email)s</a>."
#~ msgstr ""
#~ "Als je problemen hebt om een account aan te maken, neem dan contact op "
#~ "met <a href=\"mailto:%(contact_email)s\">%(contact_email)s</a>."

#~ msgid "Log in &raquo;"
#~ msgstr "Inloggen"
