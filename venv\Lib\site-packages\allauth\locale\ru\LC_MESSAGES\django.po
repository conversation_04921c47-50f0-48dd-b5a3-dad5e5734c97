# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-04-30 19:47+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Russian <https://hosted.weblate.org/projects/allauth/django-"
"allauth/ru/>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Учетная запись неактивна."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Вы не можете удалить свой основной адрес электронной почты."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Этот адрес электронной почты уже связан с этой учетной записью."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Указанные вами адрес электронной почты и/или пароль неверны."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Номер телефона и/или пароль неверны."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Пользователь с таким адресом электронной почты уже зарегистрирован."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Пожалуйста, введите свой текущий пароль."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Неверный код."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Неверный пароль."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Недействительный или просроченный ключ."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Недействительный логин."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Код сброса пароля оказался недействительным."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Вы не можете добавить более %d адресов электронной почты."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Пользователь с таким адресом электронной почты уже зарегистрирован."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr ""
"Слишком много неудачных попыток входа в систему. Повторите попытку позже."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Адрес электронной почты не закреплен ни за одной учетной записью."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Номер телефона не привязан ни к одной учетной записи пользователя."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ваш основной адрес электронной почты должен быть подтвержден."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Такое имя пользователя не может быть использовано, выберите другое."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Имя пользователя и/или пароль неверны."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Пожалуйста, выберите только один вариант."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Новое значение должно отличаться от текущего."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Используйте ваш пароль"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Используйте приложение-аутентификатор или код"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Используйте ключ безопасности"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Отметить выбранные адреса электронной почты как подтверждённые"

#: account/apps.py:11
msgid "Accounts"
msgstr "Аккаунты"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Введите номер телефона, включая код страны (например, +7 для России или "
"Казахстана)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Номер телефона"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Вы должны ввести одинаковый пароль дважды."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Пароль"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Запомнить меня"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Адрес электронной почты"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Имя пользователя"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Войти"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Имя пользователя, электронная почта или номер телефона"

#: account/forms.py:156
msgid "Username or email"
msgstr "Имя пользователя или e-mail"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Имя пользователя или номер телефона"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Электронная почта или номер телефона"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Забыли свой пароль?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-mail (ещё раз)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Подтверждение адреса электронной почты"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (опционально)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Имя пользователя (опционально)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Вы должны ввести одинаковый e-mail дважды."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Пароль (ещё раз)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Текущий пароль"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Новый пароль"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Новый пароль (ещё раз)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Код"

#: account/models.py:26
msgid "user"
msgstr "пользователь"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "адрес электронной почты"

#: account/models.py:34
msgid "verified"
msgstr "подтвержден"

#: account/models.py:35
msgid "primary"
msgstr "основной"

#: account/models.py:41
msgid "email addresses"
msgstr "адреса электронной почты"

#: account/models.py:151
msgid "created"
msgstr "создано"

#: account/models.py:152
msgid "sent"
msgstr "отправлено"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "ключ"

#: account/models.py:158
msgid "email confirmation"
msgstr "подтверждение email адреса"

#: account/models.py:159
msgid "email confirmations"
msgstr "подтверждения email адресов"

#: headless/apps.py:7
msgid "Headless"
msgstr "Безголовый"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Вы не можете добавить адрес электронной почты в учетную запись, защищенную "
"двухфакторной аутентификацией."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Вы не можете отключить двухфакторную аутентификацию."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Вы не можете генерировать коды восстановления, если не включена "
"двухфакторная аутентификация."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Вы не сможете активировать двухфакторную аутентификацию, пока не подтвердите "
"свой адрес электронной почты."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Основной ключ"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Запасной ключ"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Ключ №{number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "Многофакторная аутентификация (MFA)"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Коды восстановления"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Аутентификатор TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "Ключ WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Код аутентификатора"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Без пароля"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Включение режима работы без пароля позволяет вам войти в систему, используя "
"только этот ключ, но предъявляет дополнительные требования, такие как защита "
"биометрией или PIN-кодом."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Учетная запись с таким адресом электронной почты уже существует.Пожалуйста, "
"сначала войдите в эту учетную запись, а затем подключите %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Недействительный токен."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Для вашего аккаунта не установлен пароль."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "В вашей учетной записи нет подтвержденного адреса электронной почты."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Вы не можете отключить свою последнюю оставшуюся стороннюю учетную запись."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr ""
"Учетная запись стороннего сервиса уже подключена к другой учетной записи."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Аккаунты в социальных сетях"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "провайдер"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "провайдер ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "имя"

#: socialaccount/models.py:58
msgid "client id"
msgstr "id клиента"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ID приложения или ключ потребителя"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "секретный ключ"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Секретный ключ API, клиента или потребителя"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Ключ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "социальное приложение"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "социальные приложения"

#: socialaccount/models.py:117
msgid "uid"
msgstr "UID пользователя"

#: socialaccount/models.py:119
msgid "last login"
msgstr "дата последнего входа в систему"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "дата регистрации"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "дополнительные данные"

#: socialaccount/models.py:125
msgid "social account"
msgstr "аккаунт социальной сети"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "аккаунты социальных сетей"

#: socialaccount/models.py:160
msgid "token"
msgstr "токен"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) или access token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "секретный токен"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) или refresh token (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "истекает"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "токен социального приложения"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "токены социальных приложений"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Неверные данные профиля"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Вход"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Отмена"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Неверный ответ во время получения запроса от \"%s\". Ответ был: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Неверный ответ при получении токена доступа от \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Нет сохраненного ключа запроса для \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Нет сохраненного ключа доступа для \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Доступ к ресурсам закрыт \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Неверный ответ во время получения запроса от \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Аккаунт неактивен"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Этот аккаунт неактивен."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Мы отправили код на адрес %(recipient)s. Срок действия кода скоро истечет, "
"поэтому, пожалуйста, введите его как можно скорее."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Подтвердить"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Код запроса"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Подтвердите доступ"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""
"Пожалуйста, пройдите повторную аутентификацию, чтобы обезопасить свою "
"учетную запись."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Альтернативные варианты"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Подтверждение электронной почты"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Введите код подтверждения электронной почты"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "адрес электронной почты"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Войти"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Введите код для входа в систему"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Сброс пароля"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Введите код для сброса пароля"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Подтверждение номера телефона"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Введите код подтверждения номера телефона"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Адреса электронной почты"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "С вашей учетной записью связаны следующие адреса электронной почты:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Подтвержден"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Не подтвержден"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Основной"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Сделать основным"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Отправить подтверждение ещё раз"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Удалить"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Добавить адрес электронной почты"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Добавить E-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Вы действительно хотите удалить выбранный адрес электронной почты?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Вы получили это письмо, потому что вы или кто-то другой пытались "
"зарегистрировать учетную запись,\n"
"используя адрес электронной почты:\n"
"\n"
"%(email)s\n"
"\n"
"Однако учетная запись с таким адресом электронной почты уже существует.\n"
"В случае, если вы забыли об этом, воспользуйтесь процедурой \"Забыли "
"пароль\",\n"
"чтобы восстановить свою учетную запись:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Учетная запись уже существует"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Вас приветствует %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Благодарим вас за использование сайта «%(site_name)s!»\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Вы получили это письмо, потому что в вашей учетной записи были сделаны "
"следующие изменения:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Если вы не заметили этого изменения, пожалуйста, немедленно примите "
"надлежащие меры безопасности. Изменения в вашей учетной записи произошли с:\n"
"\n"
"- IP-адреса: %(ip)s\n"
"- Браузера: %(user_agent)s\n"
"- Дата: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""
"Ваш адрес электронной почты был изменен с %(from_email)s на %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Электронная почта изменена"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Ваш адрес электронной почты был подтвержден."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Подтверждение по электронной почте"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Вы получили это письмо, потому что пользователь %(user_display)s указал ваш "
"адрес электронной почты для регистрации учетной записи на сайте "
"%(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ваш регистрационный код указан ниже. Пожалуйста, введите его в открытом окне "
"вашего браузера."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Для подтверждения перейдите на %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Пожалуйста, подтвердите Ваш адрес электронной почты"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""
"Адрес электронной почты %(deleted_email)s был удалён из вашей учётной записи."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Email удалён"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Ваш регистрационный код указан ниже. Пожалуйста, введите его в открытом окне "
"вашего браузера."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Это письмо можно спокойно проигнорировать, если вы не инициировали это "
"действие."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Код для входа"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Ваш пароль изменён."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Пароль Изменён"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ваш код для сброса пароля указан ниже. Пожалуйста, введите его в открытом "
"окне вашего браузера."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Код для сброса пароля"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Вы получили это письмо, потому что вы или кто-то другой запросили сброс "
"пароля для вашей учетной записи.\n"
"Если это были не вы, просто проигнорируйте это письмо. Нажмите на ссылку "
"ниже, чтобы сбросить пароль."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Если вы вдруг забыли, ваше имя пользователя: %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Письмо для сброса пароля"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Ваш пароль был сброшен."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Ваш пароль был установлен."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Пароль Установлен"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Вы получили это электронное письмо, потому что вы или кто-то другой пытался "
"получить доступ к учетной записи с помощью электронной почты %(email)s. "
"Однако в нашей базе данных нет никаких записей о такой учетной записи."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Если это были вы, вы можете зарегистрировать учетную запись, перейдя по "
"ссылке ниже."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Неизвестный аккаунт"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Адрес электронной почты"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Текущий e-mail"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Изменить на"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Ваш адрес электронной почты ожидает подтверждения."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Отменить изменения"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Изменить на"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Изменить E-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Подтвердите адрес электронной почты"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Пожалуйста, подтвердите <a href=\"mailto:%(email)s\">%(email)s</a> для "
"пользователя %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Невозможно подтвердить %(email)s, потому что он уже прикреплен к другой "
"учетной записи."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ссылка некорректна или срок её действия истек. Пожалуйста, <a "
"href=\"%(email_url)s\">отправьте новый запрос на подтверждение электронной "
"почты</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Если у вас ещё нет учётной записи, пожалуйста, сначала "
"%(link)sзарегистрируйтесь%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Войти с ключом доступа"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Пришлите мне код для входа"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Выйти"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Вы уверены, что хотите выйти?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr ""
"Вы не можете удалить свой основной адрес электронной почты (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Подтверждающее письмо отправлено на %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Адрес %(email)s подтверждён."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Удален адрес электронной почты %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Успешный вход под именем %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Вы вышли."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr ""
"Код для входа был отправлен по электронной почте на адрес %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Пароль успешно изменён."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Пароль успешно установлен."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr ""
"Код для входа был отправлен по электронной почте на адрес %(recipient)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Вы только что подтвердили номер телефона %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Установлен основной адрес электронной почты."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Изменить пароль"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Забыли пароль?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Забыли свой пароль? Введите Ваш адрес электронной почты ниже, и мы отправим "
"Вам письмо, чтобы вы могли его сбросить."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Сбросить мой пароль"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Свяжитесь с нами, если у вас возникли сложности со сменой пароля."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Мы отправили вам письмо. Если вы его не получили, проверьте папку \"Спам\". "
"Свяжитесь с нами, если вы не получили письмо в течение нескольких минут."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Неправильный ключ"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Ссылка на сброс пароля неверна, вероятно, она уже была использована. Для "
"нового сброса пароля <a href=\"%(passwd_reset_url)s\">перейдите по ссылке</"
"a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Ваш пароль изменён."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Установить пароль"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Изменить номер телефона"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Текущий номер телефона"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Ваш номер телефона по-прежнему ожидает подтверждения."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Введите пароль:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Вы получите специальный код для входа в систему без пароля."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Код запроса"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Другие варианты входа"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Регистрация"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Зарегистрируйтесь"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Уже зарегистрированы? %(link)sВойдите%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Зарегистрироваться, используя ключ доступа"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Регистрация с ключом доступа"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Другие варианты"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Регистрация закрыта"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Мы сожалеем, но в текущий момент регистрация закрыта."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Заметка"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Вы уже вошли как %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Внимание:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Сейчас у вас нет прикрепленного адрес электронной почты. Рекомендуем "
"добавить, чтобы иметь возможность получать уведомления, сбрасывать пароль и "
"и т.д."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Подтвердите Ваш адрес электронной почты"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Мы отправили вам письмо с подтверждением. Перейдите по указанной ссылке, "
"чтобы завершить процесс регистрации. Если вы не видите письмо с "
"подтверждением в вашем основном почтовом ящике, проверьте папку \"Спам\"."
"Пожалуйста, свяжитесь с нами, если вы не получите письмо с подтверждением в "
"течение нескольких минут."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Эта часть сайта требует от нас подтверждения того, что\n"
"вы являетесь тем, за кого себя выдаете. Для этого нам необходимо\n"
"подтвердить собственность вашего адреса электронной почты. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Мы отправили вам письмо\n"
"для проверки. Пожалуйста, перейдите по ссылке. Если вы не видите письмо с "
"подтверждением в вашем основном почтовом ящике, проверьте папку \"Спам\".\n"
"В противном случае свяжитесь с нами, если вы не получите его в течение "
"нескольких минут."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Заметка:</strong> вы можете <a href=\"%(email_url)s\">изменить свой "
"адрес электронной почты</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Сообщения:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Меню:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Прикрепленные аккаунты"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Двухфакторная аутентификация"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Сеансы"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Ваша учетная запись защищена двухфакторной аутентификацией. Пожалуйста, "
"введите код аутентификатора:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Сгенерирован новый набор кодов восстановления двухфакторной аутентификации."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Сгенерированы Новые Коды Восстановления"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Активировано приложение Аутентификатор."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Активировано приложение для проверки подлинности"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Приложение Аутентификатор деактивировано."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Приложение для проверки подлинности отключено"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Был создан новый ключ доступа."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Создан ключ доступа"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Ключ доступа был удален."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Ключ доступа удален"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Приложение аутентификатор"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Аутентификация с помощью приложения-аутентификатора активна."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Приложение аутентификатора неактивно."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Деактивировать"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Активировать"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Ключи доступа"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Вы создали %(count)s ключ доступа."
msgstr[1] "Вы создали %(count)s клюей доступа."
msgstr[2] "Вы создали %(count)s ключей доступа."
msgstr[3] "Вы создали %(count)s ключ доступа."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Ключи доступа ещё не были созданы."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Управлять"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Добавить"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Коды восстановления"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "Доступно %(unused_count)s из %(total_count)s кодов восстановления."
msgstr[1] "Доступнен %(unused_count)s из %(total_count)s кодов восстановления."
msgstr[2] "Доступны %(unused_count)s из %(total_count)s кодов восстановления."
msgstr[3] "Доступны %(unused_count)s из %(total_count)s кодов восстановления."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Коды восстановления не установлены."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Посмотреть"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Скачать"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Генерировать"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Был создан новый набор кодов восстановления."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Ключ доступа создан."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Ключ доступа удален."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Введите код аутентификатора:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Вы собираетесь сгенерировать новый набор кодов восстановления для вашей "
"учетной записи."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Это действие аннулирует существующие коды."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Вы уверены?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Неиспользуемые коды"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Скачать коды"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Генерировать новые коды"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Активировать приложение Аутентификатор"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Чтобы защитить свою учетную запись с помощью двухфакторной аутентификации, "
"отсканируйте приведенный ниже QR-код с помощью приложения-аутентификатора. "
"Затем введите проверочный код, сгенерированный приложением ниже."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Код аутентификатора"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Вы можете сохранить этот секрет ключ и использовать его для повторной "
"установки приложения аутентификатора в будущем."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Отключите приложение Аутентификатор"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Вы собираетесь отключить аутентификацию с использованием приложения "
"Аутентификатор. Вы уверены?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Доверять этому браузеру?"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"Если вы решите доверять этому браузеру, у вас не будут запрашивать "
"проверочный код при следующем входе в систему."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Доверять в течение %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "Не доверять"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Создать ключ доступа"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Удалить ключ доступа"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Вы уверены, что хотите удалить этот ключ доступа?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Использование"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Ключ доступа (passkey)"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Ключ безопасности (Security key)"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Этот ключ не указывает, является ли он паролем доступа."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Не указано"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Добавлено %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Последний раз использовалось %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Редактировать"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Редактировать ключ безопасности"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Сохранить"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Создать ключ доступа (passkey)"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Вы собираетесь создать ключ доступа (passkey) для своей учётной записи. "
"Поскольку вы сможете добавить дополнительные ключи позже, рекомендуется "
"использовать описательное название, чтобы различать их."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Создать"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Для этой функции требуется JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Ошибка входа через внешний сервис"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"При попытке войти через учетную запись стороннего сервиса произошла ошибка."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Вы можете авторизоваться, используя следующие сервисы:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr ""
"В настоящее время у вас нет учетных записей сторонних сервисов, связанных с "
"этой учетной записью."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Добавить внешний аккаунт"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"К вашей учетной записи была подключена сторонняя учетная запись от "
"%(provider)s."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Подключена учетная запись стороннего сервиса"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"Сторонняя учетная запись от %(provider)s была отключена от вашей учетной "
"записи."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Учетная запись стороннего сервиса отключена"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Соединение с %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Вы собираетесь подключить новый сторонний аккаунт из %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Вход через %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Вы собираетесь войти, используя стороннюю учетную запись из %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Продолжить"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Авторизация отменена"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Вы прервали авторизацию, используя один из ваших аккаунтов. Если это было "
"ошибкой, перейдите к <a href=\"%(login_url)s\">авторизации</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Учетная запись стороннего сервиса подключена."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Сторонняя учетная запись была отключена."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Вы используете %(provider_name)s для авторизации на \n"
"%(site_name)s. Чтобы завершить, заполните следующую форму:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Или использовать сторонний"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Выйти из всех остальных сеансов."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Начато в"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP-адрес"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Браузер"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Последний вход в"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Текущий"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Выйти из других сеансов"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Сеансы пользователей"

#: usersessions/models.py:92
msgid "session key"
msgstr "ключ сеанса"

#, fuzzy
#~ msgid "Account Connection"
#~ msgstr "Прикрепленные аккаунты"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Минимальное количество символов в пароле: {0}."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Вы получили это письмо, потому что вы или кто-то другой запросили сброс "
#~ "пароля для вашей учетной записи.Однако нашей базе данных ничего не "
#~ "известно о пользователес электронной почтой %(email)s.\n"
#~ "\n"
#~ "Если это были не вы, просто проигнорируйте это письмо.\n"
#~ "Если же это всё-таки были вы, вы можете зарегистрировать аккаунт по "
#~ "ссылке ниже."

#, fuzzy
#~ msgid "The following email address is associated with your account:"
#~ msgstr "Следующие e-mail адреса прикреплены к вашему аккаунту:"

#, fuzzy
#~ msgid "Change Email Address"
#~ msgstr "Подтвердите e-mail адрес."

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Пожалуйста, войдите с одним\n"
#~ "из ваших внешних аккаунтов. Или <a "
#~ "href=\"%(signup_url)s\">зарегистрируйтесь</a>\n"
#~ "и авторизуйтесь на сайте %(site_name)s:"

#~ msgid "or"
#~ msgstr "или"

#~ msgid "change password"
#~ msgstr "изменить пароль"

#~ msgid "OpenID Sign In"
#~ msgstr "Войти с OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Указанный e-mail прикреплен к другому пользователю."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Мы отправили вам письмо. Пожалуйста, свяжитесь с нами, если не получили "
#~ "его в течение нескольких минут."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Логин и/или пароль не верны."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Имя пользователя может включать буквы, цифры и @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr ""
#~ "Такое имя пользователя уже используется на сайте. Пожалуйста выберите "
#~ "другое."

#, fuzzy
#~ msgid "Shopify Sign In"
#~ msgstr "Войти"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Вы подтвердили адрес <a href=\"mailto:%(email)s\">%(email)s</a> для "
#~ "пользователя %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Спасибо за использование нашего сайта!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Подтверждение выслано на %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Удалить пароль"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Вы можете удалить свой пароль, при использовании OpenID."

#~ msgid "delete my password"
#~ msgstr "удалите мой пароль"

#~ msgid "Password Deleted"
#~ msgstr "Пароль удалён"
