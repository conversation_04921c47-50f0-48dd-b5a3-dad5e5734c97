# Traducción al castellano de django-allauth
# Copyright (C) 2024 
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.61.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-04-18 00:01+0000\n"
"Last-Translator: Alex <<EMAIL>>\n"
"Language-Team: Spanish <https://hosted.weblate.org/projects/allauth/django-"
"allauth/es/>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Weblate 5.11.1-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Esta cuenta se encuentra ahora mismo desactivada."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "No puede eliminar su correo electrónico principal."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Esta dirección de correo electrónico ya está asociada a esta cuenta."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr ""
"La dirección de correo electrónico y/o la contraseña que ha indicado no son "
"correctas."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr ""
"El número de teléfono y/o la contraseña que ha indicado no son correctos."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Ya hay registrado un usuario con esta dirección de correo electrónico."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Por favor, escriba su contraseña actual."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Código incorrecto."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Contraseña incorrecta."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Clave no válida o caducada."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Login inválido."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "El token para restablecer la contraseña no es válido."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "No puede añadir más de %d direcciones de correo electrónico."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Ya hay registrado un usuario con esta dirección de correo electrónico."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Demasiados intentos fallidos. Inténtelo más tarde."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr ""
"Esta dirección de correo electrónico no está asignada a ninguna cuenta de "
"usuario."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "El número de teléfono no está asignado a ninguna cuenta de usuario."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Su dirección principal de correo electrónico debe ser verificada."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "No se puede usar este nombre de usuario. Por favor, utilice otro."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "El usuario y/o la contraseña que ha indicado no son correctos."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Por favor selecciona solo uno."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "El nuevo valor debe ser diferente del actual."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Utilice su contraseña"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Utiliza la aplicación o el código de autenticación"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Usar una clave de seguridad"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr ""
"Marcar las direcciones de correo electrónico seleccionadas como verificadas"

#: account/apps.py:11
msgid "Accounts"
msgstr "Cuentas"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr "Introduce un número incluyendo el código del país (ej. +34 para ES)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Teléfono"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Debe escribir la misma contraseña cada vez."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Contraseña"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Recordarme"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Dirección de correo electrónico"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "Correo electrónico"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Usuario"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Iniciar sesión"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Usuario, correo o teléfono"

#: account/forms.py:156
msgid "Username or email"
msgstr "Usuario o correo electrónico"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Usuario o teléfono"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Correo electrónico o teléfono"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "¿Ha olvidado tu contraseña?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "Correo electrónico (otra vez)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Confirmación de la dirección de correo electrónico"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "Correo electrónico (opcional)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Usuario (opcional)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Debe escribir el mismo correo electrónico cada vez."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Contraseña (de nuevo)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Contraseña actual"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nueva contraseña"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nueva contraseña (de nuevo)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Código"

#: account/models.py:26
msgid "user"
msgstr "usuario"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "dirección de correo electrónico"

#: account/models.py:34
msgid "verified"
msgstr "verificado"

#: account/models.py:35
msgid "primary"
msgstr "principal"

#: account/models.py:41
msgid "email addresses"
msgstr "direcciones de correo electrónico"

#: account/models.py:151
msgid "created"
msgstr "creado"

#: account/models.py:152
msgid "sent"
msgstr "enviado"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "clave"

#: account/models.py:158
msgid "email confirmation"
msgstr "confirmación de correo electrónico"

#: account/models.py:159
msgid "email confirmations"
msgstr "confirmaciones de correo electrónico"

#: headless/apps.py:7
msgid "Headless"
msgstr "Headless"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"No puede añadir una dirección de correo electrónico a una cuenta protegida "
"por la autenticación de dos factores."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "No se puede desactivar la autenticación de dos factores."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"No puedes generar códigos de recuperación sin tener activada la "
"autenticación de dos factores."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"No puede activar la autenticación de dos factores hasta que haya verificado "
"su dirección de correo electrónico."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Llave maestra"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Clave de respaldo"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Clave n° {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Códigos de recuperación"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Verificador TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Código de del autenticador"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Passwordless"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Habilitar la operación sin contraseña le permite iniciar sesión usando solo "
"esta clave, pero impone requisitos adicionales como biometría o protección "
"con PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Ya existe una cuenta con esta dirección de correo electrónico. Por favor, "
"primero inicie sesión con esa cuenta, y después vincule su cuenta %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Token no válido."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Su cuenta no tiene ninguna contraseña."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Su cuenta no tiene ninguna dirección de correo electrónico verificada."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "No puede desconectar la última cuenta de terceros restante."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Esta cuenta externa ya está asociada a otra cuenta."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Cuentas de redes sociales"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "proveedor"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID del proveedor"

#: socialaccount/models.py:56
msgid "name"
msgstr "nombre"

#: socialaccount/models.py:58
msgid "client id"
msgstr "identificador del cliente"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "Identificador de App, o clave de consumidor"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "clave secreta"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr ""
"Clave secreta de la API, clave secreta del cliente o clave secreta del "
"consumidor"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Clave"

#: socialaccount/models.py:81
msgid "social application"
msgstr "aplicación de redes sociales"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "aplicaciones de redes sociales"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "último inicio de sesión"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "fecha de incorporación"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "datos extra"

#: socialaccount/models.py:125
msgid "social account"
msgstr "cuenta de redes sociales"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "cuentas de redes sociales"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) o token de acceso (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "clave secreta del token"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) o token de refresco (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "expira el"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token de aplicación de redes sociales"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokens de aplicación de redes sociales"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Datos de perfil no válidos"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Iniciar sesión"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Cancelar"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Respuesta no válida al obtener token de solicitud de \"%s\". La respuesta "
"fue: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Respuesta no válida al obtener el token de acceso de \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "No hay token de solicitud guardado para \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "No hay token de acceso guardado para \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Sin acceso a recursos privados de \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Respuesta no válida al obtener token de solicitud de \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Cuenta desactivada"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Esta cuenta está desactivada."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Hemos enviado un código a %(recipient)s. El código caduca en breve, así que "
"introdúcelo pronto."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Confirmar"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Solicite el código"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Confirmar acceso"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Por favor, vuelva a autenticarse para proteger su cuenta."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Opciones alternativas"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Verificación del correo electrónico"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Introduce el código de verificación del correo electrónico"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "dirección de correo electrónico"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Iniciar sesión"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Ingrese el código de inicio de sesión"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Restablecer Contraseña"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Introduce el código · Restablecer Contraseña"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Reenviar Verificación telefónica"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Introduce el código de verificación del teléfono"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Direcciones de correo electrónico"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr ""
"Las siguientes direcciones de correo electrónico están asociadas a su cuenta:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Verificado"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Sin verificar"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Principal"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Definir como principal"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Reenviar Verificación"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Eliminar"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Añadir correo electrónico"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Añadir correo electrónico"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr ""
"¿Seguro que desea eliminar la dirección de correo electrónico seleccionada?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Usted está recibiendo este correo electrónico porque usted u otra persona "
"intentó registrarse para una\n"
"cuenta utilizando la siguiente dirección de correo electrónico:\n"
"\n"
"%(email)s\n"
"\n"
"Sin embargo, ya existe una cuenta con esa dirección de correo electrónico. "
"En caso de que lo haya\n"
"olvidado, utilice el procedimiento de recuperació de contraseña olvidada "
"para recuperar\n"
"su cuenta:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "La cuenta ya existe"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "¡Hola de parte de %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"¡Gracias por usar %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Está recibiendo este correo porque se ha realizado el siguiente cambio en su "
"cuenta:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Si no reconoce este cambio, tome inmediatamente las precauciones de "
"seguridad adecuadas. El cambio en su cuenta se origina en:\n"
"\n"
"- Dirección IP: %(ip)s\n"
"- Navegador: %(user_agent)s\n"
"- Fecha: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Tu email ha sido cambiado de %(from_email)s a %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Correo electrónico modificado"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Su correo electrónico ha sido verificado."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Correo electrónico confirmado"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Estás recibiendo este correo electrónico porque el usuario %(user_display)s "
"ha facilitado tu dirección de correo electrónico para registrar una cuenta "
"en %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Tu código de verificación de correo electrónico aparece a continuación. Por "
"favor, introdúcelo en la ventana abierta de tu navegador."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Para confirmar que esto es correcto, vete a %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Por favor, confirme su dirección de correo electrónico"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""
"La dirección de correo electrónico %(deleted_email)s ha sido eliminada de tu "
"cuenta."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Correo electrónico eliminado"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Su código de inicio de sesión se enumera a continuación. Introdúzcalo en la "
"ventana abierta de su navegador."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Este correo puede ser ignorado con seguridad si usted no inició esta acción."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Código de inicio de sesión"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Su contraseña ha sido modificada."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Contraseña modificada"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Su código de restablecimiento de contraseña se enumera a continuación. "
"Introdúzcalo en la ventana abierta de su navegador."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Código de restablecimiento de contraseña"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Está recibiendo este correo electrónico porque usted u otra persona ha "
"solicitado un restablecimiento de contraseña para su cuenta de usuario.\n"
"Puede ignorar este correo si no ha solicitado este cambio. Haga clic en el "
"siguiente enlace para restablecer su contraseña."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "En caso de haberlo olvidado, su usuario es %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Correo electrónico para restablecer contraseña"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Su contraseña ha sido restablecida."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Su contraseña ha sido establecida."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Establecer contraseña"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Está recibiendo este correo electrónico porque usted, u otra persona, "
"intentó acceder a una cuenta con email %(email)s. Sin embargo, no tenemos "
"constancia de dicha cuenta en nuestra base de datos."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Si ha sido usted, puede registrarse para obtener una cuenta utilizando el "
"siguiente enlace."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Cuenta desconocida"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Correo electrónico"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Correo electrónico actual"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Cambiando a"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Su dirección de correo electrónico sigue pendiente de verificación."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Cancelar el cambio"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Cambiar a"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Cambiar correo electrónico"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Confirmar dirección de correo electrónico"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Por favor, confirme que <a href=\"mailto:%(email)s\">%(email)s</a> es una "
"dirección de correo electrónico para el usuario %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"No se puede confirmar %(email)s porque ya está confirmado por otra cuenta."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Este enlace de confirmación por correo electrónico ha caducado o no es "
"válido. Por favor, <a href=\"%(email_url)s\"> emita una nueva solicitud de "
"confirmación por correo electrónico</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Si aún no ha creado una cuenta, por favor %(link)sregístrese%(end_link)s "
"primero."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Iniciar sesión con un passkey"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Envíame un código de inicio de sesión"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Cerrar sesión"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "¿Seguro que desea cerrar sesión?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "No puede eliminar su correo electrónico principal (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Correo electrónico de confirmación enviado a %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Ha confirmado %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Correo electrónico %(email)s eliminado."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Ha iniciado sesión exitosamente como %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Ha cerrado sesión."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr ""
"Se ha enviado un código de inicio de sesión por correo a %(recipient)s ."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Contraseña cambiada con éxito."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Contraseña establecida con éxito."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr ""
"Se ha enviado un código de inicio de sesión por correo a %(recipient)s ."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Has verificado el número de teléfono %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Dirección principal de correo electrónico establecida."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Cambiar la contraseña"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "¿Olvidó su contraseña?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"¿Ha olvidado su contraseña? Introduzca su dirección de correo electrónico, y "
"le enviaremos un correo que le permitirá restablecerla."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Restablecer mi contraseña"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Si tiene alguna dificultad para restablecer su contraseña, por favor "
"contáctenos."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Le hemos enviado un correo electrónico. Si no lo ha recibido, compruebe su "
"carpeta de correo no deseado. De lo contrario, póngase en contacto con "
"nosotros si no lo recibe en unos minutos."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Hay un problema con el token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"El enlace para restablecer la contraseña es inválido, probablemente porque "
"ya ha sido utilizado. Por favor solicite <a "
"href=\"%(passwd_reset_url)s\">restablecer la contraseña de nuevo</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Su contraseña ha cambiado."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Establecer contraseña"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Cambiar a teléfono"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Teléfono actual"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Su número de teléfono sigue pendiente de verificación."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Introduzca su contraseña:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Recibirá un código especial para iniciar sesión sin contraseña."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Solicite el código"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Otras opciones de inicio de sesión"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registro"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Registrarse"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "¿Ya tiene una cuenta? Por favor %(link)sinicie sesión%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Regístrate usando una passkey"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Regítrate con passkey"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Otras opciones"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registro cerrado"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Lo sentimos, en este momento el registro está cerrado."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Ya ha iniciado sesión como %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Advertencia:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Actualmente no tienes ninguna dirección de correo electrónico configurada. "
"Debería añadir una dirección de correo electrónico para recibir "
"notificaciones, restablecer su contraseña, etc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifique su dirección de correo electrónico"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Le hemos enviado un correo electrónico para su verificación. Siga el enlace "
"proporcionado para finalizar el proceso de registro. Si no ves el correo "
"electrónico de verificación en tu bandeja de entrada principal, comprueba tu "
"carpeta de correo no deseado. Por favor, póngase en contacto con nosotros si "
"no recibe el correo electrónico de verificación en unos minutos."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Esta parte de la página requiere que verifiquemos que\n"
"usted es quien dice ser. Para ello, le pedimos que\n"
"verifique la titularidad de su dirección de correo electrónico. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Le hemos enviado un correo electrónico para\n"
"verificación. Por favor, haga clic en el enlace que aparece en ese correo "
"electrónico. Si no ve el correo electrónico de verificación en su bandeja de "
"entrada principal, compruebe su carpeta de correo no deseado. De lo "
"contrario,\n"
"póngase en contacto con nosotros si no lo recibe en unos minutos."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> aún puedes <a href=\"%(email_url)s\">cambiar tu "
"dirección de correo electrónico</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Mensajes:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menú:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Conexiones de Cuenta"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Autenticació de doble factor"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sesiones"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Su cuenta está protegida por doble factor de autenticación. Por favor, entre "
"el código del autenticador:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Se ha generado un nuevo conjunto de códigos de recuperación de Autenticación "
"de Dos Factores."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Generados nevos códigos de recuperación"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "La aplicación de autenticación ha sido activada."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Aplicación de autenticación activada"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Aplicación de autenticación desactivada."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Aplicación de autenticación desactivada"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Se ha agregado una nueva clave de seguridad."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Clave de seguridad añadida"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Se ha eliminado una clave de seguridad."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Clave de seguridad eliminada"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Aplicación de autenticación"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "La autenticación con una aplicación de autenticación está activa."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "No hay ninguna aplicación de autenticación activa."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Desactivar"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Activar"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Claves de seguridad"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Has añadido %(count)s clave."
msgstr[1] "Has añadido %(count)s claves."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "No se han agregado claves de seguridad."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Administrar"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Añadir"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Códigos de recuperación"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Hay %(unused_count)s de %(total_count)s código de recuperación disponibles."
msgstr[1] ""
"Hay %(unused_count)s de %(total_count)s códigos de recuperación disponibles."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "No se han configurado códigos de recuperación."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Mostrar"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Descargar"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generar"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Se ha generado un nuevo conjunto de código de recuperación."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Clave de seguridad añadida."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Clave de seguridad eliminada."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Ingrese un código de autenticación:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Está a punto de generar un nuevo conjunto de código de recuperación para su "
"cuenta."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Esta operación invalidará sus códigos existentes."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "¿Está seguro?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Códigos no utilizados"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Descarga los códigos"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Crear nuevos códigos"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Activar la aplicación de autenticación"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Escanee el código QR a continuación con su aplicación de autenticación para "
"proteger su cuenta con autenticación de dos factores. Luego, ingrese el "
"código de verificación generado por la aplicación a continuación."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Secreto de autenticador"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Puede guardar este secreto y usarlo más tarde para reinstalar su aplicación "
"de autenticación."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deshabilitar la aplicación de autenticación"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Está a punto de desactivar la autenticación a través de la aplicación de "
"autenticación. ¿Está seguro?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Confía en este navegador?"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"Si decides confiar en este navegador, no se te pedirá un código de "
"verificación la próxima vez que inicies sesión."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Confía durante %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "No confíes"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Añadir una clave de seguridad"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Eliminar clave de seguridad"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "¿Estás seguro de que deseas eliminar esta clave de seguridad?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Uso"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Passkey"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Clave de seguridad"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Esta clave no indica si es una clave de acceso."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Sin especificar"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Añadida el %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Usada por última vez %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Editar"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Editar la clave de seguridad"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Guardar"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Crear una passkey"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Estás a punto de crear una passkey para tu cuenta. Como puedes añadir claves "
"adicionales más adelante, puedes utilizar un nombre descriptivo para "
"diferenciar las claves."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Crear"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Esta funcionalidad requiere JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Falló la autenticación de terceros"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Se produjo un error al intentar iniciar sesión a través de su cuenta de "
"terceros."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Puede iniciar sesión en su cuenta utilizando una de las siguientes cuentas "
"de terceros:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Actualmente no tiene cuentas de terceros conectadas a esta cuenta."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Agregar una cuenta de terceros"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Una cuenta de terceros de %(provider)s se ha conectado a su cuenta."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Cuenta de terceros vinculada"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"La cuenta de terceros desde %(provider)s ha sido desvinculada de tu cuenta."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Cuenta de terceros desconectada"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Conectar %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Se dispone a conectarse a una nueva cuenta externa de %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Inicio de sesión mediante %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Se dispone a iniciar sesión usando una cuenta externa desde %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Continuar"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Inicio de sesión cancelado"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Ha decidido cancelar el inicio de sesión en nuestro sitio usando una de sus "
"cuentas. Si ha sido un error, por favor <a href=\"%(login_url)s\">inicie "
"sesión</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "La cuenta externa ha sido conectada."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "La cuenta externa ha sido desconectada."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Se dispone a usar su cuenta de %(provider_name)s para acceder a "
"%(site_name)s.\n"
"Como paso final, por favor complete el siguiente formulario:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "O use una cuenta de terceros"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Se han cerrado todas las demás sesiones."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Iniciada el"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Dirección IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Navegador"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Visto por última vez"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Actual"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Cerrar las demás sesiones"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Sesiones de usuario"

#: usersessions/models.py:92
msgid "session key"
msgstr "clave de sesión"

#~ msgid "Account Connection"
#~ msgstr "Conexiones de la cuenta"

#~ msgid "Use security key or device"
#~ msgstr "Usar clave o dispositivo de seguridad"

#~ msgid "Add Security Key or Device"
#~ msgstr "Agregar clave de seguridad o dispositivo"

#~ msgid "Add key or device"
#~ msgstr "Agregar clave o dispositivo"

#~ msgid "Security Keys and Devices"
#~ msgstr "Dispositivos y claves de seguridad"

#~ msgid "You have not added any security keys/devices."
#~ msgstr "No ha agregado ninguna clave/dispositivo de seguridad."

#~ msgid "Edit Security Key or Device"
#~ msgstr "Editar clave o dispositivo de seguridad"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "La contraseña necesita al menos {0} caracteres."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Está recibiendo este correo electrónico porque usted u otra persona ha "
#~ "solicitado una\n"
#~ "contraseña para su cuenta de usuario. Sin embargo, no tenemos ningún "
#~ "registro de un usuario\n"
#~ "con email %(email)s en nuestra base de datos.\n"
#~ "\n"
#~ "Este correo puede ser ignorado con seguridad si usted no solicitó un "
#~ "restablecimiento de contraseña.\n"
#~ "\n"
#~ "Si has sido tú, puedes registrarte para obtener una cuenta utilizando el "
#~ "siguiente enlace."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr ""
#~ "Las siguientes direcciones de correo electrónico están asociadas a su "
#~ "cuenta:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Confirmar dirección de correo electrónico"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Por favor, inicie sesión con una\n"
#~ "cuenta de otra red social. O %(link)sregístrese%(end_link)s \n"
#~ "como usuario de %(site_name)s e inicie sesión a continuación:"

#~ msgid "or"
#~ msgstr "o"

#~ msgid "change password"
#~ msgstr "cambiar la contraseña"

#~ msgid "OpenID Sign In"
#~ msgstr "Iniciar sesión con OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Este correo electrónico ya está asociado con otra cuenta."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Le hemos enviado un correo electrónico. Por favor contáctenos si no lo "
#~ "recibe en unos minutos."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr ""
#~ "El correo electrónico/usuario y/o la contraseña que especificó no son "
#~ "correctos."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Los nombres de usuarios pueden contener solamente letras, números, y @/./"
#~ "+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Este usuario ya está en uso. Por favor elije otro."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Iniciar sesión"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Has confirmado que <a href=\"mailto:%(email)s\">%(email)s</a> es una "
#~ "dirección de correo electrónico del usuario %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "¡Gracias por utilizar nuestro sitio!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Correo de confirmación enviado a %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Eliminar Contraseña"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Puedes eliminar tu contraseña ya que ingresaste con OpenID."

#~ msgid "delete my password"
#~ msgstr "eliminar mi contraseña"

#~ msgid "Password Deleted"
#~ msgstr "Contraseña Eliminada"
