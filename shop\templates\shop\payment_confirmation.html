<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmed -JPR Dry Fish </title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .success-icon {
            font-size: 60px;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .order-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
        }
        .payment-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .bank-details {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        .cod-info {
            background: #d4edda;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #3367d6;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .instructions {
            margin-top: 15px;
        }
        .instructions ul {
            margin-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
        }
        .transaction-id {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">✅</div>
            <h1>Order Confirmed!</h1>
            <p>Thank you for your order</p>
        </div>
        
        <div class="content">
            <!-- Order Details -->
            <div class="order-details">
                <h2>Order Details</h2>
                <div class="detail-row">
                    <span>Order ID:</span>
                    <span>#{{ order.id }}</span>
                </div>
                <div class="detail-row">
                    <span>Product:</span>
                    <span>{{ order.get_product_display }}</span>
                </div>
                <div class="detail-row">
                    <span>Quantity:</span>
                    <span>{{ order.quantity }} kg</span>
                </div>
                <div class="detail-row">
                    <span>Customer:</span>
                    <span>{{ order.name }}</span>
                </div>
                <div class="detail-row">
                    <span>Mobile:</span>
                    <span>{{ order.mobile }}</span>
                </div>
                <div class="detail-row">
                    <span>Delivery Address:</span>
                    <span>
                        {% if order.street_name or order.place_name or order.city %}
                            {{ order.get_full_address }}
                        {% else %}
                            {{ order.address }}
                        {% endif %}
                    </span>
                </div>
                <div class="detail-row">
                    <span>Payment Method:</span>
                    <span>{{ order.get_payment_method_display }}</span>
                </div>
                {% if order.transaction_id %}
                <div class="detail-row">
                    <span>Transaction ID:</span>
                    <span class="transaction-id">{{ order.transaction_id }}</span>
                </div>
                {% endif %}
                <div class="detail-row">
                    <span>Total Amount:</span>
                    <span>₹{% if order.payment_method == 'cash_on_delivery' %}{{ order.total_amount|add:50 }}{% else %}{{ order.total_amount }}{% endif %}</span>
                </div>
            </div>

            <!-- Payment Method Specific Information -->
            {% if order.payment_method == 'cash_on_delivery' %}
            <div class="cod-info">
                <h3>💵 Cash on Delivery Instructions</h3>
                <div class="instructions">
                    <ul>
                        <li>Pay cash when your order is delivered</li>
                        <li>Please keep exact change ready</li>
                        <li>Our delivery person will provide a receipt</li>
                        <li>COD charges: ₹50 (included in total)</li>
                        <li>Delivery within 2-3 business days</li>
                    </ul>
                </div>
            </div>
            {% endif %}

            {% if order.payment_method == 'bank_transfer' and bank_details %}
            <div class="bank-details">
                <h3>🏛️ Bank Transfer Details</h3>
                <div class="instructions">
                    <p><strong>Bank Name:</strong> {{ bank_details.bank_name }}</p>
                    <p><strong>Account Holder:</strong> {{ bank_details.account_holder }}</p>
                    <p><strong>Account Number:</strong> {{ bank_details.account_number }}</p>
                    <p><strong>IFSC Code:</strong> {{ bank_details.ifsc_code }}</p>
                    <p><strong>Branch:</strong> {{ bank_details.branch }}</p>
                    <br>
                    <p><strong>Instructions:</strong></p>
                    <ul>
                        <li>Transfer the exact amount: ₹{{ order.total_amount }}</li>
                        <li>Use Transaction ID: {{ order.transaction_id }} as reference</li>
                        <li>Send payment screenshot to our WhatsApp</li>
                        <li>Order will be processed after payment verification</li>
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- General Information -->
            <div class="payment-info">
                <h3>📋 What's Next?</h3>
                <div class="instructions">
                    <ul>
                        <li>You will receive an order confirmation SMS</li>
                        <li>Our team will contact you for delivery details</li>
                        <li>Track your order status in your account</li>
                        <li>For any queries, contact us via WhatsApp</li>
                    </ul>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="{% url 'home' %}" class="btn btn-success">Continue Shopping</a>
                <a href="{% url 'view_orders' %}" class="btn">View My Orders</a>
            </div>
        </div>
    </div>
</body>
</html>
