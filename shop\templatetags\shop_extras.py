from django import template
from ..models import ProductReview

register = template.Library()

@register.simple_tag
def get_product_reviews(product_name):
    """Get review data for a product"""
    return {
        'average_rating': ProductReview.get_average_rating(product_name),
        'review_count': ProductReview.get_review_count(product_name),
        'star_display': ProductReview.get_star_display_for_product(product_name),
    }

@register.filter
def get_star_rating(rating):
    """Convert numeric rating to star display"""
    if rating:
        full_stars = int(rating)
        half_star = 1 if rating - full_stars >= 0.5 else 0
        empty_stars = 5 - full_stars - half_star
        
        stars = '★' * full_stars
        if half_star:
            stars += '☆'
        stars += '☆' * empty_stars
        return stars
    return '☆☆☆☆☆'

@register.inclusion_tag('shop/partials/product_rating.html')
def show_product_rating(product_name):
    """Show product rating with stars"""
    return {
        'product_name': product_name,
        'average_rating': ProductReview.get_average_rating(product_name),
        'review_count': ProductReview.get_review_count(product_name),
        'star_display': ProductReview.get_star_display_for_product(product_name),
    }

@register.simple_tag
def generate_quantity_options():
    """Generate quantity options for select dropdown"""
    quantities = []
    # Generate from 100g to 10kg
    for grams in range(100, 1000, 100):  # 100g to 900g
        quantities.append((grams, f"{grams}g"))

    for kg in [1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 6, 7, 8, 9, 10]:  # 1kg to 10kg
        grams = int(kg * 1000)
        if kg == int(kg):
            display = f"{int(kg)}kg"
        else:
            display = f"{kg}kg"
        quantities.append((grams, display))

    return quantities
