# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2013.
# <PERSON><PERSON> <<EMAIL>>, 2018.
# Filip <PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.55\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-09-25 06:15+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech <https://hosted.weblate.org/projects/allauth/django-"
"allauth/cs/>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"
"X-Generator: Weblate 5.8-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Účet je v tuto chvíli neaktivní."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Nemůžete odstranit primární e-mailovou adresu."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Tento e-mail je již k tomuto účtu přiřazen."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Zadaný e-mail nebo heslo není správné."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "Zadané uživatelské jméno nebo heslo není správné."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Uživatel s tímto e-mailem je již registrován."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Prosím, zadejte svoje současné heslo."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Nesprávný kód."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Nesprávné heslo."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Neplatný klíč."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Neplatný token."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Token pro reset hesla není platný."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Nelze přidat více než %d e-mailových adres."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Uživatel s tímto e-mailem je již registrován."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Příliš mnoho pokusů o přihlášení. Zkuste to prosím později."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "E-mail není přiřazen k žádnému účtu"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "E-mail není přiřazen k žádnému účtu"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Vaše primární e-mailová adresa musí být ověřena."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Toto uživatelské jméno nemůže být zvoleno. Prosím, zvolte si jiné."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Zadané uživatelské jméno nebo heslo není správné."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Zadejte své heslo"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Použijte ověřovací aplikaci nebo kód"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Zadejte tajný klíč"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Označ vybrané e-mailové adresy jako ověřené"

#: account/apps.py:11
msgid "Accounts"
msgstr "Účty"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Hesla se musí shodovat."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Heslo"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Zapamatovat"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-mailová adresa"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Uživatelské jméno"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Přihlášení"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Uživatelské jméno nebo e-mail"

#: account/forms.py:156
msgid "Username or email"
msgstr "Uživatelské jméno nebo e-mail"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Uživatelské jméno nebo e-mail"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "E-mail (nepovinné)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Zapomněli jste heslo?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-mail (znovu)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Potrvzení e-mailové adresy"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (nepovinné)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "E-mail (nepovinné)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Vložené e-maily se musí shodovat."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Heslo (znovu)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Současné heslo"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nové heslo"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nové heslo (znovu)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Kód"

#: account/models.py:26
msgid "user"
msgstr "uživatel"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "e-mailová adresa"

#: account/models.py:34
msgid "verified"
msgstr "ověřeno"

#: account/models.py:35
msgid "primary"
msgstr "primární"

#: account/models.py:41
msgid "email addresses"
msgstr "e-mailové adresy"

#: account/models.py:151
msgid "created"
msgstr "vytvořeno"

#: account/models.py:152
msgid "sent"
msgstr "odeslaný"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "klíč"

#: account/models.py:158
msgid "email confirmation"
msgstr "Potvrzovací e-mail"

#: account/models.py:159
msgid "email confirmations"
msgstr "Ověřovací e-maily"

#: headless/apps.py:7
msgid "Headless"
msgstr "Headless"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Nelze přidat e-mailovou adresu k účtu chráněnému dvoufaktorovouautentizací."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Dvoufaktorové ověřování nelze deaktivovat."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Nelze generovat kódy pro obnovení bez aktivovaného dvoufaktorového ověřování."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Nemůžete aktivovat dvoufaktorovou autentizaci, dokud nepotvrdíte svoue-"
"mailovou adresu."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Hlavní klíč"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Záložní klíč"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Klíč #{number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "2FA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Záchranné kódy"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP Autentifikátor"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Kód autentifikátoru"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Bez hesla"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Povolení možnosti bez hesla vám umožní přihlásit se pouze pomocí tohoto "
"klíče, ale klade další požadavky, jako je biometrie nebo ochrana PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Účet s touto e-mailovou adresou již existuje. Prosím přihlaste se nejdříve "
"pod tímto účtem a potom připojte svůj %s účet."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Neplatný token."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Váš účet nemá nastavené heslo."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Váš účet nemá žádný ověřený e-mail."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Nemůžete odpojit svůj poslední externí účet."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Externí účet je již spojen s jiným účtem."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Účty sociálních sítí"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "poskytovatel"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID poskytovatele"

#: socialaccount/models.py:56
msgid "name"
msgstr "jméno"

#: socialaccount/models.py:58
msgid "client id"
msgstr "id klienta"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App ID nebo uživatelský klíč"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "tajný klíč"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "tajný API klíč, tajný klientský klíč nebo uživatelský tajný klíč"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Klíč"

#: socialaccount/models.py:81
msgid "social application"
msgstr "sociální aplikace"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "sociální aplikace"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "poslední přihlášení"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "datum registrace"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "extra data"

#: socialaccount/models.py:125
msgid "social account"
msgstr "účet sociální sítě"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "účty sociálních sítí"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) nebo přístupový token (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "tajný token"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) nebo token pro obnovu (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "vyprší"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token sociální aplikace"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokeny sociálních aplikací"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Neplatná data profilu"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Přihlášení"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Zrušit"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Chyba při odesílání požadavku: \"%s\". Odpoveď byla: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Chyba při získávání přístupového klíče od \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Není uložen žádný požadavkový klíč pro: \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Není uložen žádný přístupový klíč pro: \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Není přístup k privátním zdrojům: \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Chyba při získávání požadavkového klíče \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Neaktivní účet"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Tento účet není aktivní."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Na adresu %(email_link)s jsme poslali ověřovací kód. Jeho platnost brzy "
"vyprší, zadejte ho prosím včas."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Potvrdit"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Požádat kód"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Potvrdit přístup"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Pro ochranu účtu se prosím znovu ověřte."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Alternativní možnosti"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Ověření e-mailu"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Zadejte ověřovací kód e-mailu"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "e-mailová adresa"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Přihlásit se"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Zadejte Ověřovací Kód"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Reset hesla"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Reset hesla"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Znovu zaslat oveřovací e-mail"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "Zadejte ověřovací kód e-mailu"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mailové adresy"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "K vašemu účtu jsou přiřazeny tyto e-mailové adresy:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Ověřeno"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Neověřeno"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Primární"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Zvolit jako primární"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Znovu zaslat oveřovací e-mail"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Odstranit"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Přidat e-mailovou adresu"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Přidat e-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Opravdu chcete odstranit zvolené e-mailové adresy?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Tento e-mail jste obdrželi, protože vy nebo někdo jiný se pokusil "
"registrovat účet s \n"
"použitím e-mailové adresy:\n"
"\n"
"%(email)s\n"
"\n"
"Ale účet s touto e-mailovou adresou již existuje. Pokud jste na to "
"zapomněli, \n"
"použijte prosím postup obnovení hesla k obnovení vašeho účtu:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Účet již existuje"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Pozdrav z %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Děkujeme, že používáte %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Tento e-mail jste obdrželi, protože ve vašem účtu byla provedena následující "
"změna:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Pokud tuto změnu nepoznáváte, prosím okamžitě proveďte náležitá bezpečnostní "
"opatření. Změna vašeho účtu pochází z:\n"
"\n"
"- IP adresa: %(ip)s\n"
"- Prohlížeč: %(user_agent)s\n"
"- Datum %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Váš e-mail byl změněn z %(from_email)s na %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Email změněn"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Váš email byl potvrzen."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Potvrzení E-mailu"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Tento e-mail jste obdrželi protože uživatel %(user_display)s zadal vaši e-"
"mailovou adresu k registraci účtu na stránkách %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Níže najdete váš ověřovací kód. Zadejte jej do otevřeného okna prohlížeče."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Pro potvrzení, že je to v pořádku, pokračujte na %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Potvrďte prosím svou e-mailovou adresu"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "E-mailová adresa %(deleted_email)s byla odstraněna z vašeho účtu."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Email odstraněn"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Níže najdete váš přihlašovací kód. Zadejte jej do otevřeného okna prohlížeče."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Tento e-mail může být bezpečně ignorován, pokud jste tuto akci nezahájili vy."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Přihlašovací kód"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Vaše heslo bylo změněno."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Heslo změněno"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Níže najdete váš přihlašovací kód. Zadejte jej do otevřeného okna prohlížeče."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Reset hesla"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Tento e-mail jste obdrželi protože jste vy nebo někdo jiný zažádal o změnu "
"hesla uživatelského účtu.\n"
"Pokud jste to nebyli vy, můžete tento e-mail ignorovat. Pokud ano, klikněte "
"na odkaz níže pro změnu vašeho hesla."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr ""
"Pro případ, že byste zapomněli, vaše uživatelské jméno je %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail pro reset hesla"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Vaše heslo bylo obnoveno."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Vaše heslo bylo nastaveno."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Nastavení hesla"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Tento e-mail jste obdrželi, protože jste se vy nebo někdo jiný pokusili o "
"přístup k účtu s e-mailem %(email)s. V naší databázi však žádný záznam o "
"takovém účtu nemáme."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Pokud jste to byli vy, můžete si účet zaregistrovat pomocí odkazu níže."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Neznámý účet"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-mailové adresa"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Aktuální email"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Změněn na"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Vaše primární e-mailová adresa stále čeká na ověření."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Zrušit změnu"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Změnit na"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Změnit E-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Potvrzení e-mailové adresy"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prosím, potvrďte, že <a href=\"mailto:%(email)s\">%(email)s</a> je e-mailová "
"adresa pro uživatele %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Nelze potvrdit %(email)s, protože již byl spojen s jiným účtem."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Tento ověřovací odkaz již vypršel nebo není správný. Prosím, <a "
"href=\"%(email_url)s\">zažádejte si o nový</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Pokud jste si ještě nevytvořili účet, nejprve se "
"%(link)szaregistrujte%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Přihlaste se pomocí přístupového klíče"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Pošlete mi přihlašovací kód"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Odhlásit se"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Jste si jisti, že se chcete odhlásit?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Nemůžete odstranit primární e-mailovou adresu (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Ověření e-mailu posláno na %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Ověřili jste %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mailová adresa %(email)s byla odebrána."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Úspěšně přihlášen jako %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Právě jste byl odhlášen."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Přihlašovací kód byl zaslán na adresu %(email)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Heslo bylo úspěšně změněno."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Heslo bylo úspěšně nastaveno."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Přihlašovací kód byl zaslán na adresu %(email)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primární e-mail byla nastavena."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Změnit heslo"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Zapomenuté heslo?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Zapomněli jste heslo? Zadejte prosím svoji e-mailovou adresu a do e-mailové "
"schránky Vám přijde návod na jeho obnovu."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Resetovat moje heslo"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Prosím, kontaktujte nás, pokud máte jakékoliv potíže s resetováním hesla."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Poslali jsme vám e-mail. Pokud jste ho neobdrželi, zkontrolujte prosím "
"složku s nevyžádanou poštou (spam). V opačném případě nás kontaktujte, pokud "
"ho neobdržíte do několika minut."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Chybný klíč"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Odkaz na resetování hesla byl neplatný, možná proto, že již byl použit. "
"Požádejte prosím o <a href=\"%(passwd_reset_url)s\">nový reset hesla</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Vaše heslo je nyní změněno."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Nastavit heslo"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "Změnit na"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "Aktuální"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "Vaše primární e-mailová adresa stále čeká na ověření."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Zadejte své heslo:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "Obdržíte e-mail obsahující speciální kód pro přihlášení bez hesla."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Požádat kód"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Další možnosti přihlášení"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Zaregistrovat se"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Zaregistrovat se"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Máte již účet? %(link)sPřihlašte se%(end_link)s, prosím."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Zaregistrujte se pomocí přístupového klíče"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Registrace přístupového klíče"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Další možnosti"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registrace je uzavřena"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Omlouváme se, ale registrace je momentálně uzavřena."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Poznámka"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Již jste přihlášen jako %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Varování:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"V současné chvíli nemáte nastaveny žádné e-mailové adresy. Prosím, uložte si "
"k účtu alespoň jeden e-mail, abyste moli dostávat upozornění nebo mohli "
"použít funkci zapomenutého hesla apod."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Ověřte svoji e-mailovou adresu"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Byl vám zaslán ověřovací e-mail. Následujte odkaz v e-mailu pro dokončení "
"registračního procesu. Pokud jste ho neobdrželi, zkontrolujte prosím složku "
"s nevyžádanou poštou (spam). Neváhejte nás kontaktovat v případě, pokud e-"
"mail do několika minut neobdržíte."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Tato část stránek vyžaduje ověření,\n"
"že jste ten, kdo tvrdíte. K těmto účelům požadujeme\n"
"aby jste ověřil vlastnictví své e-mailové adresy. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Zaslali jsme vám email pro oveření.\n"
"Prosím, klikněte na odkaz uvnitř e-mailu. Pokud jste ho neobdrželi, "
"zkontrolujte prosím složku s nevyžádanou poštou (spam).\n"
"Neváhejte nás kontaktovat v případě, pokud e-mail nedostanete do několika "
"minut."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Poznámka:</strong> stále můžete <a href=\"%(email_url)s\">změnit "
"vaši e-mailovou adresu.</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Zprávy:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Propojení účtu"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Dvoufaktorová autentizace"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Relace"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Váš účet je chráněn dvoufaktorovou autentizací. Prosím, zadejte autentizační "
"kód:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Byla vygenerována nová sada obnovovacích kódů dvoufaktorové autentizace."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Nové obnovovací kódy vygenerovány"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Autentifikátor byl aktivován."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Autentifikátor byl aktivován"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Autentifikátor byl deaktivován."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Autentifikátor byl deaktivován"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Byl přidán nový bezpečnostní klíč."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Bezpečnostní klíč přidán"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Bezpečnostní klíč byl odstraněn."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Bezpečnostní klíč odstraněn"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Autentifikátor"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Autentizace pomocí autentifikátoru je aktivní."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Autentifikátor není aktivní."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktivovat"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktivovat"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Bezpečnostní klíče"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Přidali jste %(count)s bezpečnostní klíč."
msgstr[1] "Přidali jste %(count)s bezpečnostní klíče."
msgstr[2] "Přidali jste %(count)s bezpečnostního klíče."
msgstr[3] "Přidali jste %(count)s bezpečnostních klíčů."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Nebyly přidány žádné bezpečnostní klíče."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Spravovat"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Přidat"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Záchranné kódy"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Z dostupných záchranných kódů je použit %(unused_count)s z celkového počtu "
"%(total_count)s kódů."
msgstr[1] ""
"Z dostupných záchranných kódů jsou použity %(unused_count)s z celkového "
"počtu %(total_count)s kódů."
msgstr[2] ""
"Z dostupných záchranných kódů je použito %(unused_count)s z celkového počtu "
"%(total_count)s kódů."
msgstr[3] ""
"Z dostupných záchranných kódů je použitých %(unused_count)s z celkového "
"počtu %(total_count)s kódů."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Nejsou nastaveny žádné záchranné kódy."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Zobrazit"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Stáhnout"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generovat"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Byla vygenerována nová sada záchranných kódů."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Bezpečnostní klíč přidán."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Bezpečnostní klíč odstraněn."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Zadejte ověřovací kód:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Chystáte se vygenerovat novou sadu obnovovacích kódů pro váš účet."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Tato akce zruší platnost vašich stávajících kódů."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Jste si jistý?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Nepoužité kódy"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Stáhnout kódy"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Generovat nové kódy"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Aktivovat Autentifikátor"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Chcete-li svůj účet chránit pomocí dvoufaktorového ověřování, naskenujte "
"pomocí autentikační aplikace uvedený QR kód. Poté níže zadejte ověřovací kód "
"vygenerovaný aplikací."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Klíč autentifikátoru"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Tento klíč můžete uložit a později použít k opětovné instalaci aplikace "
"autentikátoru."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deaktivovat Autentifikátor"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Chystáte se deaktivovat autentizaci pomocí autentifikátoru. Jste si jisti?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Přidat tajný klíč"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Odebrat tajný klíč"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Jste si jisti, že se chcete odebrat tajný klíč?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Použito"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Přístupový klíč"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Bezpečnostní klíč"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Tento klíč neoznačuje, zda se jedná o přístupový klíč."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Nespecifikováno"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Přidáno %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Naposledy použito %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Upravit"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Upravit bezpečnostní klíč"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Uložit"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Vytvořte přístupový klíč"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Chystáte se vytvořit přístupový klíč pro svůj účet. Protože později můžete "
"přidat další klíče, můžete klíče odlišit pomocí popisného názvu."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Vytvořit"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Tato funkce vyžaduje JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Přihlášení pomocí externího účtu selhalo"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Nastala chyba při přihlašování pomocí externího účtu."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Můžete se přihlásit pomocí jakéhokoliv následujícího externího účtu:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "V současné chvíli nemáte připojeny žádné další účty."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Přidejte další externí účet"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "K vašemu účtu byl připojen další účet od %(provider)s."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Externí účet přidán"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Externí účet od %(provider)s byl odpojen od vašeho účtu."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Externí účet odpojen"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Připojit %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Chystáte se připojit nový externí účet od %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Přihlásit se pomocí %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Chystáte se přihlásit pomocí externího účtu od %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Pokračovat"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Přihlášení zrušeno"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Rozhodli jste se zrušit přihlašování jednoho z vašich účtů. Pokud je to "
"omylem, následujte <a href=\"%(login_url)s\">přihlášení</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Externí účet byl připojen."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Externí účet byl odpojen."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Chystáte se použít vášeho %(provider_name)s účtu k přihlášení na naše "
"stránky \n"
"%(site_name)s. Jako poslední krok, prosím, vyplňte následující formulář:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Nebo použijte externí účet"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Odhlášen ze všech ostatních relací."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Začala v"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP adresa"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Prohlížeč"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Napoposledy použita v"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Aktuální"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Odhlásit ostatní relace"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Uživatelské relace"

#: usersessions/models.py:92
msgid "session key"
msgstr "klíč relace"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Propojení účtu"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Heslo musí obsahovat minimálně {0} znaků."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Tento e-mail jste obdrželi protože jste vy nebo někdo jiný zažádal o "
#~ "změnu \n"
#~ "hesla uživatelského účtu. Nicméně, nemáme žádný záznam o uživateli s \n"
#~ "e-mailem %(email)s v naší databázi.\n"
#~ "\n"
#~ "Tento e-mail můžete bezpečně ignorovat, pokud jste nezažádali o změnu "
#~ "hesla.\n"
#~ "\n"
#~ "Jestliže jste to byli vy, můžete se zaregistrovat na stránkách pomocí "
#~ "odkazu \n"
#~ "níže."

#~ msgid "The following email address is associated with your account:"
#~ msgstr "K vašemu účtu je přiřazena tato e-mailová adresa:"

#~ msgid "Change Email Address"
#~ msgstr "Změna e-mailové adresy"

#~ msgid ""
#~ "To safeguard the security of your account, please enter your password:"
#~ msgstr "Pro zabezpečení vašeho účtu, prosím, zadejte vaše heslo:"

#, fuzzy
#~| msgid "Generate"
#~ msgid "Regenerate"
#~ msgstr "Generovat"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Přihlašte se prosím výběrem jednoho\n"
#~ "z vašich účtů třetích stran. Nebo se <a "
#~ "href=\"%(signup_url)s\">zaregistruje</a> na stránky %(site_name)s a "
#~ "přihlašte se níže:"

#~ msgid "or"
#~ msgstr "nebo"

#~ msgid "change password"
#~ msgstr "změnit heslo"

#~ msgid "OpenID Sign In"
#~ msgstr "Přihlášení OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Tento e-mail je již přiřazen k jinému účtu."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Zaslali jsme vám e-mail. Prosím, kontaktujte nás, pokud ho nedostanete do "
#~ "několika minut."

#~| msgid "The password reset token was invalid."
#~ msgid "The provided password is not valid."
#~ msgstr "Použité heslo není platné."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Zadané přihlašovací údaje nejsou správné."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr ""
#~ "Uživatelské jméno může obsahovat pouze písmena, číslice a znaky @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Toto uživatelské jméno je již zvoleno. Prosím, vyberte si jiné."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Přihlásit se"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Potvrdili jste e-mailovou adresu <a href=\"mailto:%(email)s\">%(email)s</"
#~ "a> uživateli %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Děkujeme za využívání našich stránek!"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "Ověřovací e-mail byl zaslán: %(email)s"

#~ msgid "Delete Password"
#~ msgstr "Smazat heslo"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "Můžete si smazat heslo, protože používáte jiné způsoby přihlášení."

#~ msgid "delete my password"
#~ msgstr "Odstanit moje heslo"

#~ msgid "Password Deleted"
#~ msgstr "Heslo bylo odstraněno"
