from django import forms
from .models import Order, ProductReview


class OrderForm(forms.ModelForm):
    """Form for creating orders with detailed delivery address"""

    def __init__(self, *args, **kwargs):
        # Extract pre-filled data from kwargs
        self.hide_product_fields = kwargs.pop('hide_product_fields', False)
        self.prefilled_product = kwargs.pop('prefilled_product', None)
        self.prefilled_quantity = kwargs.pop('prefilled_quantity', None)

        super().__init__(*args, **kwargs)

        # Generate quantity choices from 100g to 10kg in 100g steps
        quantity_choices = [('', 'Select quantity...')]
        for grams in range(100, 10100, 100):  # 100g to 10kg in 100g steps
            if grams >= 1000:
                kg = grams / 1000
                if kg == int(kg):
                    display = f"{int(kg)}kg"
                else:
                    display = f"{kg:.1f}kg"
            else:
                display = f"{grams}g"
            quantity_choices.append((str(grams), display))

        self.fields['quantity_grams'].widget.choices = quantity_choices

        # Hide product and quantity fields if they're pre-filled
        if self.hide_product_fields:
            self.fields['product'].widget = forms.HiddenInput()
            self.fields['quantity_grams'].widget = forms.HiddenInput()

            # Set initial values if provided
            if self.prefilled_product:
                self.fields['product'].initial = self.prefilled_product
            if self.prefilled_quantity:
                self.fields['quantity_grams'].initial = self.prefilled_quantity
    
    class Meta:
        model = Order
        fields = [
            'name', 'email', 'mobile', 'address',
            'street_name', 'place_name', 'city', 'state', 'pin_code',
            'product', 'quantity_grams'
        ]
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your full name',
                'required': True
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your email address',
                'required': True
            }),
            'mobile': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your mobile number',
                'type': 'tel',
                'required': True
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your complete delivery address (optional if detailed address is provided)',
                'rows': 3,
                'required': False
            }),
            'street_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'House number, street name',
                'required': True
            }),
            'place_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Area, locality, landmark',
                'required': True
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'City name',
                'required': True
            }),
            'state': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'State name',
                'required': True
            }),
            'pin_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'PIN code',
                'pattern': '[0-9]{6}',
                'maxlength': '6',
                'required': True
            }),
            'product': forms.Select(attrs={
                'class': 'form-control',
                'required': True
            }),
            'quantity_grams': forms.Select(attrs={
                'class': 'form-control quantity-select',
                'required': True,
                'onchange': 'updatePrice()'
            })
        }
        
        labels = {
            'name': 'Full Name',
            'email': 'Email Address',
            'mobile': 'Mobile Number',
            'address': 'Additional Address Info (Optional)',
            'street_name': 'Street Address',
            'place_name': 'Area/Locality',
            'city': 'City',
            'state': 'State',
            'pin_code': 'PIN Code',
            'product': 'Select Product',
            'quantity_grams': 'Quantity'
        }
    
    def clean_mobile(self):
        """Validate mobile number"""
        mobile = self.cleaned_data.get('mobile')
        if mobile:
            # Remove any non-digit characters
            mobile = ''.join(filter(str.isdigit, mobile))
            if len(mobile) != 10:
                raise forms.ValidationError("Mobile number must be 10 digits long.")
            if not mobile.startswith(('6', '7', '8', '9')):
                raise forms.ValidationError("Please enter a valid Indian mobile number.")
        return mobile
    
    def clean_pin_code(self):
        """Validate PIN code"""
        pin_code = self.cleaned_data.get('pin_code')
        if pin_code:
            # Remove any non-digit characters
            pin_code = ''.join(filter(str.isdigit, pin_code))
            if len(pin_code) != 6:
                raise forms.ValidationError("PIN code must be 6 digits long.")
        return pin_code
    
    def clean_quantity_grams(self):
        """Validate quantity in grams"""
        quantity_grams = self.cleaned_data.get('quantity_grams')
        if quantity_grams:
            try:
                quantity_grams = int(quantity_grams)
                if quantity_grams < 100:
                    raise forms.ValidationError("Minimum quantity is 100g.")
                if quantity_grams > 10000:
                    raise forms.ValidationError("Maximum quantity is 10kg.")
                if quantity_grams % 100 != 0:
                    raise forms.ValidationError("Quantity must be in multiples of 100g.")
            except (ValueError, TypeError):
                raise forms.ValidationError("Please enter a valid quantity.")
        return quantity_grams
    
    def clean(self):
        """Additional form validation"""
        cleaned_data = super().clean()
        
        # Ensure either detailed address fields are provided or the general address field
        street_name = cleaned_data.get('street_name')
        place_name = cleaned_data.get('place_name')
        city = cleaned_data.get('city')
        state = cleaned_data.get('state')
        pin_code = cleaned_data.get('pin_code')
        address = cleaned_data.get('address')
        
        # Check if detailed address fields are provided
        detailed_address_provided = all([street_name, place_name, city, state, pin_code])
        
        if not detailed_address_provided and not address:
            raise forms.ValidationError(
                "Please provide either complete detailed address information or fill the general address field."
            )
        
        return cleaned_data


class ProductReviewForm(forms.ModelForm):
    """Form for submitting product reviews"""

    class Meta:
        model = ProductReview
        fields = ['product', 'rating', 'review_text']

        widgets = {
            'product': forms.Select(attrs={
                'class': 'form-control',
                'required': True
            }),
            'rating': forms.Select(
                choices=[(i, f'{i} Star{"s" if i != 1 else ""}') for i in range(1, 6)],
                attrs={
                    'class': 'form-control',
                    'required': True
                }
            ),
            'review_text': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Share your experience with this product (optional)',
                'rows': 4,
                'maxlength': 1000
            })
        }

        labels = {
            'product': 'Product',
            'rating': 'Rating',
            'review_text': 'Review (Optional)'
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # If product is provided in initial data, make it readonly
        if self.initial.get('product'):
            self.fields['product'].widget.attrs['readonly'] = True
            # Don't disable the field as it won't be submitted
            # Instead, we'll handle this in clean method

    def clean(self):
        cleaned_data = super().clean()
        product = cleaned_data.get('product')

        if self.user and product:
            # Check if user has already reviewed this product
            existing_review = ProductReview.objects.filter(
                user=self.user,
                product=product
            ).exclude(pk=self.instance.pk if self.instance.pk else None)

            if existing_review.exists():
                raise forms.ValidationError(
                    f"You have already reviewed {dict(ProductReview.PRODUCT_CHOICES)[product]}. "
                    "You can only review each product once."
                )

            # Check if user has ordered this product
            user_orders = Order.objects.filter(
                user=self.user,
                product=product,
                payment_status='completed'
            )

            if not user_orders.exists():
                raise forms.ValidationError(
                    f"You can only review products you have purchased. "
                    f"Please place an order for {dict(ProductReview.PRODUCT_CHOICES)[product]} first."
                )

        return cleaned_data

    def save(self, commit=True):
        review = super().save(commit=False)
        if self.user:
            review.user = self.user
        if commit:
            review.save()
        return review
