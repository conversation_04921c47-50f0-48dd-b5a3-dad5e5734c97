# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-11-03 04:02+0000\n"
"Last-Translator: Milan <PERSON> <<EMAIL>>\n"
"Language-Team: Slovak <https://hosted.weblate.org/projects/allauth/django-"
"allauth/sk/>\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"
"X-Generator: Weblate 5.8.2\n"
"X-Translated-Using: django-rosetta 0.9.9\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Tento účet nie je momentálne aktívny."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Primárna e-mailová adresa sa nedá odstrániť."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Táto e-mailová adresa je už spojená s týmto účtom."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Uvedený e-mail alebo heslo nie je správne."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "Uvedené užívateľské meno alebo heslo nie je správne."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Používateľ s touto e-mailovou adresou už existuje."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Prosím, napíšte svoje súčasné heslo."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Nesprávny kód."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Nesprávne heslo."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Neplatný kľúč."

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "Neplatný token."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Token na obnovu hesla bol nesprávny."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Nemôžte pridať viac než %d e-mailových adries."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Používateľ s touto e-mailovou adresou už existuje."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Príliš veľa neúspešných pokusov o prihlásenie. Skúste neskôr."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr ""
"Táto e-mailová adresa nie je pridelená k žiadnemu používateľskému kontu"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr ""
"Táto e-mailová adresa nie je pridelená k žiadnemu používateľskému kontu"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Primárna e-mailová adresa musí byť overená."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Užívateľské meno nemôže byť použité. Prosím, použite iné meno."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Uvedené užívateľské meno alebo heslo nie je správne."

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Použite svoje heslo"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Použiť autentifikačnú aplikáciu alebo kód"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Použiť bezpečnostný kľuč"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Označiť vybrané adresy ako overené"

#: account/apps.py:11
msgid "Accounts"
msgstr "Účty"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Heslá sa nezhodujú."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Heslo"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Zapamätať si ma"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "E-mailová adresa"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Užívateľské meno"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "Užívateľské meno alebo e-mail"

#: account/forms.py:156
msgid "Username or email"
msgstr "Užívateľské meno alebo e-mail"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "Užívateľské meno alebo e-mail"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "E-mail (nepovinné)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Zabudnuté heslo?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-mail (znova)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Potvrdenie e-mailu"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-mail (nepovinné)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "E-mail (nepovinné)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "E-maily sa nezhodujú."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Heslo (znovu)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Súčasné heslo"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nové heslo"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nové heslo (znovu)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Kód"

#: account/models.py:26
msgid "user"
msgstr "používateľ"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "e-mailová adresa"

#: account/models.py:34
msgid "verified"
msgstr "overený"

#: account/models.py:35
msgid "primary"
msgstr "primárny"

#: account/models.py:41
msgid "email addresses"
msgstr "e-mailové adresy"

#: account/models.py:151
msgid "created"
msgstr "vytvorený"

#: account/models.py:152
msgid "sent"
msgstr "odoslané"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "kľúč"

#: account/models.py:158
msgid "email confirmation"
msgstr "potvrdenie e-mailu"

#: account/models.py:159
msgid "email confirmations"
msgstr "potvrdenia e-mailu"

#: headless/apps.py:7
msgid "Headless"
msgstr "Bezhlavný"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Nemôžete si pridať ďalšiu emailovú adresu do účtu s dvojfaktorovou "
"autentifikáciou."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Nemôžete si deaktivovať dvojfaktorovú autentifikáciu."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Nemôžete si vygenerovať záložné kódy bez aktívnej dvojfaktorovej "
"autentifikácie."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Nemôžete aktivovať dvojfaktorovú autentifikáciu pokiaľ nemáte verifikovanú "
"svoju adresu."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Hlavný kľúč"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Záložný kľúč"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Kľúč č. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Kódy obnovy"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP autentifikátor"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Kód z autentifikátora"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Prihlásenie bez hesla"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Aktivácia prihlásenia bez hesla vám dovolí prihlásiť sa len pomocou tohto "
"kľúča, ale vyžaduje dodatočné požiadavky ako napríklad PIN alebo biometriu."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Účet s touto e-mailovou adresou už existuje. Prosím, prihláste sa najprv pod "
"daným účtom a potom pripojte svoj %s účet."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Neplatný token."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Váš účet nemá nastavené heslo."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Váš účet nemá overenú e-mailovú adresu."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "Nemôžete si odstrániť svoj posledný účet tretej strany."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Tento účet tretej strany už je priradený k inému používateľovi."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Účty na sociálnych sieťach"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "poskytovateľ"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID poskytovateľa"

#: socialaccount/models.py:56
msgid "name"
msgstr "meno"

#: socialaccount/models.py:58
msgid "client id"
msgstr "identifikátor klienta"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ID aplikácie alebo zákaznícky kľúč"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "tajný kľúč"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Kľúč API, klienta alebo zákazníka"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Kľúč"

#: socialaccount/models.py:81
msgid "social application"
msgstr "sociálna aplikácia"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "sociálne aplikácie"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "posledné prihlásenie"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "dáum pripojenia"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "ďalšie údaje"

#: socialaccount/models.py:125
msgid "social account"
msgstr "sociálny účet"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "sociálne účty"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""
"\"Oauth_token\" (Podpora protokolu OAuth1) alebo prístup tokenu (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "heslo prístupového tokenu"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""
"\"Oauth_token_secret\" (Podpora protokolu OAuth1) alebo token obnovenie "
"(OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "vyexpiruje"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token sociálnej aplikácie"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokeny sociálnej aplikácie"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Nesprávne profilové údaje"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Prihlásiť sa"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Zrušiť"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Neplatná odpoveď pri získavaní request tokenu z \"%s\". Odpoveď: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Neplatná odozva pri získavaní prístupu tokenu z \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Žiadna uložená požiadavka tokenu pre \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Žiadny uložený prístupový token pre \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Žiadny prístup do privátneho úložiska na \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Neplatná odpoveď pri získavaní request tokenu z \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Účet neaktívny"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Tento účet je neaktívny."

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Odoslali sme overovací kód na adresu %(email_link)s. Platnosť tohto kódu "
"čoskoro vyprší, zadajte ho čo najskôr."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Potvrdiť"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Vyžiadať kód"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Potvrdiť prístup"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Znova sa overte, aby ste ochránili svoj účet."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Alternatívne možnosti"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Overenie emailovej adresy"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Zadajte kód na overenie emailovej adresy"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "e-mailová adresa"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Prihlásiť sa"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Zadanie prihlasovacieho kódu"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Obnoviť heslo"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "Obnoviť heslo"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "Preposlať overenie"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter Email Verification Code"
msgid "Enter Phone Verification Code"
msgstr "Zadajte kód na overenie emailovej adresy"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-mailová adresa"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "Nasledujúce e-mailové adresy sú prepojené s vašim účtom:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Overený"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Neoverený"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Primárny"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Vytvoriť primárny"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Preposlať overenie"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Odstrániť"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Pridať e-mailovú adresu"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Pridať e-mail"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Naozaj chcete odstrániť vybranú e-mailovú adresu?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Tento e-mail ste dostali, pretože ste sa vy, alebo niekto iný\n"
"pokúšali vytvoriť účet pre e-mailovu adresu:\n"
"\n"
"%(email)s\n"
"\n"
"Účet s touto e-mailovou adresou už však existuje.  V prípade,\n"
"že ste na svoju registráciu zabudli, použite prosím funkciu obnovy\n"
"hesla vášho účtu:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Účet už existuje"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Dobrý deň z %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Ďakujeme za využitie %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "Tento email dostávate kvôli nasledujúcim zmenám vo vašom účte:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Ak ste tieto zmeny nevykonali vy, prosím okamžite prijmite náležité "
"bezpečnostné opatrenia. Zmeny vo vašom účte pochádzajú z:\n"
"\n"
"- IP adresa: %(ip)s\n"
"- Prehliadač: %(user_agent)s\n"
"- Dátum: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Vaša emailová adresa bola zmenená z %(from_email)s na %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Emailová adresa zmenená"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Vaša emailová adresa bola overená."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Overenie emailovej adresy"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Tento email dostávate, pretože používateľ %(user_display)s zadal vašu "
"emailovú adresu na registráciu účtu na %(site_domain)s ."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Váš kód na overenie emailovej adresy je uvedený nižšie. Prosím, zadajte ho "
"do otvoreného okna vo vašom prehliadači."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Pre overenie správnosti navštívte %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Potvrďte prosím svoju e-mailovú adresu"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "Emailová adresa %(deleted_email)s bola odstránená z vášho účtu."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Emailová adresa odstránená"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Váš prihlasovací kód je uvedený nižšie. Prosím, zadajte ho do otvoreného "
"okna vo vašom prehliadači."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "Tento email môžete kľudne ignorovať, ak ste si nevyžiadali túto akciu."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Prihlasovací kód"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Vaše heslo bolo zmenené."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Heslo zmenené"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Váš prihlasovací kód je uvedený nižšie. Prosím, zadajte ho do otvoreného "
"okna vo vašom prehliadači."

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "Obnoviť heslo"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Tento e-mail ste dostali, pretože niekto požiadal o heslo k vášmu "
"používateľskému účtu.\n"
"Ak ste to neboli vy, správu môžete pokojne ignorovať. Kliknutím na odkaz "
"nižšie obnovíte svoje heslo."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Ak ste náhodou zabudli, vaše používateľské meno je %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-mail pre obnovu hesla"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Vaše heslo bolo obnovené."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Vaše heslo bolo nastavené."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Heslo nastavené"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Tento email dostávate, pretože ste sa vy, alebo niekto iný pokúsili "
"pristúpiť k účtu s emailovou adresou %(email)s. Avšak, neevidujeme žiaden "
"účet s danou adresou v našom systéme."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Ak ste to boli vy, môžete si zaregistrovať nový účet navštívením linku "
"nižšie."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Neznámy účet"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "E-mailová adresa"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Súčasná emailová adresa"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Zmeniť na"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Vaša emailová adresa stále čaká na overenie."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Zrušiť zmenu"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Zmeniť na"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Zmeniť e-mail"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Potvrdiť e-mailovú adresu"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prosím potvrďte, že <a href=\"mailto:%(email)s\">%(email)s</a> je e-mailová "
"adresa pre používateľa %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Nemožno potvrdiť %(email)s pretože už je potvrdený iným používateľom."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Odkaz na potvrdenie e-mailu je neplatný alebo vypršal. <a "
"href=\"%(email_url)s\">Zaslať novú žiadosť o overovací e-mail</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Ak ešte nemáte vytvorený účet, tak potom sa prosím najprv "
"%(link)szaregistrujte%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Prihlásiť sa prístupovým kľúčom"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "Poslať prihlasovací kód"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Odhlásiť"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Ste si istý, že sa chcete odhlásiť?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Primárna e-mailová adresa sa nedá odstrániť (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Overovací e-mail poslaný na %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s potvrdené."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-mailová adresa %(email)s úpešne odstránená."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Úspešne ste sa prihlásili ako %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Odhlásili ste sa."

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Prihlasovací kód bol odoslaný na %(email)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Zmena hesla prebehla úspešne."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr ")Nastavenie hesla bolo úspešné."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Prihlasovací kód bol odoslaný na %(email)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primárna e-mailová adresa bola úspešne zadaná."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Zmeniť heslo"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Zabudnuté heslo?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Zabudli ste heslo? Vložte nižšie svoju e-mailovú adresu a čoskoro vám "
"pošleme e-mail na obnovenie hesla."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Obnov moje heslo"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Prosím, kontaktujte nás, ak máte nejaký problém s obnovením svojho hesla."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Poslali sme vám email. Ak ste ho nedostali, skontrolujte si priečinok "
"nevyžiadanej pošty. Ak ho v priebehu pár minút neobdržíte, kontaktujte nás."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Zlý token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Odkaz na obnovu heslo je neplatný, pravdepodobne už bol použitý. <a "
"href=\"%(passwd_reset_url)s\">Nové obnovenie hesla</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Tvoje heslo bolo zmenené."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Nastaviť heslo"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "Zmeniť na"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "Aktuálna relácia"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "Vaša emailová adresa stále čaká na overenie."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Zadajte svoje heslo:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "Obdržíte email obsahujúci kód, ktorý použijete na prihlásenie."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Vyžiadať kód"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Iné možnosti prihlásenia"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrácia"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Zaregistrovať"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Už ste sa zaregistrovali? Tak sa %(link)sprihláste%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Ste pripravený/á nájsť svojho ideálneho partnera"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Passkey Zaregistrujte sa"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Ďalšie možnosti"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registrácia uzavretá"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Ospravedlňujeme sa, ale registrácia je momentálne uzavretá."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Poznámka"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Už ste prihlásený ako %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Varovanie:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Momentálne nemáte nastavený žiaden e-mail, kvôli čomu nemôžete dostávať "
"upozornenia, obnovovať heslo, atď."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Potvrďte e-mailovú adresu"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Poslali sme Vám overovací e-mail. Kliknutím na uvedený odkaz dokončite "
"proces registrácie. Ak ste ho nedostali, skontrolujte si priečinok so "
"spamom. V prípade, že do niekoľkých minút nedostanete overovací e-mail, "
"kontaktujte nás."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Na prezeranie nasledujúceho obsahu je potrebné overenie vašej e-mailovej "
"adresy. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Poslali sme vám overovací e-mail. Kliknite prosím na odkaz v emaili. Ak ste "
"email neobdržali, skontrolujte priečinok nevyžiadanej pošty. V prípade, že "
"ho do niekoľkých minút nedostanete, kontaktujte nás."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Poznámka:</strong> stále môžete <a href=\"%(email_url)s\">zmeniť "
"svoju e-mailovú adresu</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Správy:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Pripojenia účtu"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Dvojfaktorová autentifikácia"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Relácie"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Váš účet je chránený dvojfaktorovu autentifikáciou. Prosím zadajte kód z "
"autentifikátora:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "Boli vygenerované nové záložné kódy pre dvojfaktorovú autentifikáciu."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Nové záložné kódy"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Autentifikačná aplikácia aktivovaná."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Aktivácia autentifikačnej aplikácie"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Autentifikačná aplikácia deaktivovaná."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Deaktivácia autentifikačnej aplikácie"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Bol pridaný nový bezpečnostný kľúč."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Pridaný bezpečnostný kľúč"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Bezpečnostný kľúč bol odstránený."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Odstránený bezpečnostný kľúč"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Autentifikačná aplikácia"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Autentifikácia pomocou aplikácie je aktívna."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Autentifikačná aplikácia je neaktívna."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktivovať"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktivovať"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Bezpečnostné kľúče"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Pridali ste si %(count)s bezpečnostný kľúč."
msgstr[1] "Pridali ste si %(count)s bezpečnostné kľúče."
msgstr[2] "Pridali ste si %(count)s bezpečnostných kľúčov."
msgstr[3] "Pridali ste si %(count)s bezpečnostných kľúčov."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Nepridali ste si žiadne bezpečnostné kľúče."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Spravovať"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Pridať"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Kódy obnovy"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Je k dispozícii %(unused_count)s kódov z celkového počtu %(total_count)s "
"kódov obnovy."
msgstr[1] ""
"Je k dispozícii %(unused_count)s kód z celkového počtu %(total_count)s kódov "
"obnovy."
msgstr[2] ""
"Je k dispozícii %(unused_count)s kódov z celkového počtu %(total_count)s "
"kódov obnovy."
msgstr[3] ""
"Je k dispozícii %(unused_count)s kódu z celkového počtu %(total_count)s "
"kódov obnovy."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Nie sú nastavené žiadne kódy obnovy."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Zobraziť"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Stiahnuť"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generovať"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Boli vygenerované nové kódy obnovy."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Bezpečnostný kľúč pridaný."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Bezpečnostný kľúč odstránený."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Zadajte kód z autentifikátora:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Chystáte sa vygenerovať nové kódy obnovy pre váš účet."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Táto akcia zruší platnosť vašich existujúcich kódov."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Ste si istý?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Nepoužité kódy"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Stiahnuť kódy"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Generovať nové kódy"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Aktivovať autentifikačnú aplikáciu"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Na ochranu vášho účtu dvojfaktorovou autentifikáciou naskenujte QR kód "
"nižšie vašou autentifikačnou aplikáciou a následne zadajte nižšie "
"vygenerovaný overovací kód."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Heslo prístupového tokenu"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Môžete si uložiť toto heslo a kedykoľvek ho použiť na preinštalovanie vašej "
"autentifikačnej aplikaćie."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deaktivovať autentifikačnú aplikáciu"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "Chystáte sa deaktivovať vašu autentifikačnú aplikáciu. Ste si istý?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Pridať bezpečnostný kľúč"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Odstrániť bezpečnostný kľúč"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Ste si istý, že chcete odstrániť tento bezpečnostný kľúč?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Využitie"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Prístupový kľúč"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Bezpečnostný kľúč"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Tento kľúč neuvádza, či ide o prístupový kľúč."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Neuvedené"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Pridaný %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Naposledy použitý %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Upraviť"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Upraviť bezpečnostný kľúč"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Uložiť"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Vytvoriť Passkey"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Chystáte sa vytvoriť prihrávku pre váš účet. Ako môžete pridať ďalšie kľúče "
"neskôr, môžete použiť descriptive meno povedať kľúče od seba."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Vytvorte"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Táto funkcionalita vyžaduje JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Zlyhanie prihlasovania tretej strany"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "Pri prihlasovaní sa cez účet tretej strany nastala chyba."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "Môžete sa prihlásiť pomocou niektorého z nasledujúcich účtov:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Momentálne nemáte priradený žiaden účet tretej strany."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Pridať účet tretej strany"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Účet tretej strany %(provider)s bol priradený do vášho účtu."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Priradený účet tretej strany"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Účet tretej strany %(provider)s bol odstránený z vášho účtu."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Odstránený účet tretej strany"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Prepojiť s %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Chystáte sa prepojiť nový účet tretej strany %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Prihlásiť sa cez %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Chystáte sa prihlásiť cez účet tretej strany %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Pokračovať"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Prihlásenie zrušené"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Rozhodli ste sa zrušiť prihlasovanie sa na našu stránku použitím jedného z "
"vašich existujúcich účtov. Ak se chceli vykonať inú operáciu, pokračujte na "
"<a href=\"%(login_url)s\">prihlásiť sa</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Účet tretej strany bol priradený."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Účet tretej strany bol odstránený."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Chystáte sa použiť váš %(provider_name)s účet na prihlásenie do\n"
"%(site_name)s. Ako posledný krok vyplňte nasledujúci formulár:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Alebo použiť tretiu stranu"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Boli ste odhlásený zo všetkých ostatných relácií."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Začiatok"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP adresa"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Prehliadač"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Naposledy použitá"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Aktuálna relácia"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Odhlásiť sa z ostatných relácií"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Relácie používateľa"

#: usersessions/models.py:92
msgid "session key"
msgstr "klúč relácie"

#~ msgid "Account Connection"
#~ msgstr "Pripojené účty"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Heslo musí mať aspoň {0} znakov."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Tento e-mail ste dostali, pretože niekto požiadal o heslo k Vášmu\n"
#~ "používateľskému účtu. V našej databáze však nemáme žiadny záznam o "
#~ "používateľovi\n"
#~ "s e-mailom %(email)s.\n"
#~ "\n"
#~ "Ak ste nežidali o obnovenie hesla, môžete tento e-mail pokojne "
#~ "ignorovať.\n"
#~ "\n"
#~ "Ak ste to boli Vy, môžete si zaregistrovať účet pomocou odkazu nižšie."

#~ msgid "The following email address is associated with your account:"
#~ msgstr "Nasledujúce e-mailové adresy sú prepojené s vašim účtom:"

#~ msgid "Change Email Address"
#~ msgstr "Potvrdiť e-mailovú adresu"

#~ msgid ""
#~ "To safeguard the security of your account, please enter your password:"
#~ msgstr "Kvôli bezpečnosti vášho účtu, zadajte prosím vaše heslo:"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Prosím prihláste sa s jedným\n"
#~ "z vašich existujúcich účtov iných služieb. Alebo <a "
#~ "href=\"%(signup_url)s\">sa zaregistrujte</a>\n"
#~ "na %(site_name)s a prihláste sa nižšie:"

#~ msgid "or"
#~ msgstr "alebo"

#~ msgid "change password"
#~ msgstr "zmeniť heslo"

#~ msgid "OpenID Sign In"
#~ msgstr "Prihlásiť pomocou OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Táto e-mailová adresa je už spojená s iným účtom."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Odoslali sme vám e-mail. Prosím kontaktujte nás ak ste ho nedostali do "
#~ "pár minút."
