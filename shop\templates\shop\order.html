{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Dry Fish - JPR Dry Fish</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> JP Dry Fish
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                <li><a href="/order/" class="active">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="container" style="padding-top: 2rem;">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 style="font-size: 2.5rem; color: #1f2937; margin-bottom: 0.5rem;">
                <i class="fas fa-shopping-cart"></i> Place Your Order
            </h1>
            <p style="color: #6b7280; font-size: 1.1rem;">Fill in your details to get fresh dry fish delivered</p>
        </div>

        <!-- User Welcome Message -->
        {% if user.is_authenticated %}
            <div style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem; border-left: 4px solid #3b82f6;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-user-check" style="color: #3b82f6;"></i>
                    <strong style="color: #1e40af;">Welcome, {{ user.first_name|default:user.email }}!</strong>
                </div>
                <p style="margin: 0.5rem 0 0 0; color: #1e40af;">You're logged in with your Google account. Your order will be linked to your account for easy tracking.</p>
            </div>
        {% endif %}

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Order Form -->
        <div class="form-container">
            <form method="post" class="fade-in-up">
                {% csrf_token %}

                <!-- Pre-filled Product Information -->
                <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem; text-align: center;">
                    <h3 style="margin: 0 0 0.5rem 0; font-size: 1.5rem;">
                        <i class="fas fa-fish"></i> {{ product_display_name }}
                    </h3>
                    <p style="margin: 0; font-size: 1.2rem; opacity: 0.9;">
                        <i class="fas fa-weight-hanging"></i> Quantity: {{ quantity_display }}
                    </p>
                    {{ form.product }}
                    {{ form.quantity_grams }}
                </div>

                <div class="form-group">
                    <label for="name">
                        <i class="fas fa-user"></i> Full Name
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="error-message">
                            {% for error in form.name.errors %}
                                <small style="color: #dc3545;">{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i> Email Address
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="error-message">
                            {% for error in form.email.errors %}
                                <small style="color: #dc3545;">{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="mobile">
                        <i class="fas fa-phone"></i> Mobile Number
                    </label>
                    <input type="tel" id="mobile" name="mobile" class="form-control"
                           placeholder="Enter your mobile number"
                           value="{{ form.mobile.value|default:'' }}" required>
                </div>

                <!-- Detailed Delivery Address Section -->
                <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 1.5rem; border-radius: 12px; margin: 1.5rem 0; border-left: 4px solid #28a745;">
                    <h4 style="color: #155724; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-map-marker-alt"></i> Delivery Address Details
                    </h4>
                    <p style="color: #155724; margin-bottom: 1rem; font-size: 0.9rem;">
                        Please provide your complete delivery address for accurate delivery.
                    </p>

                    <div class="form-group">
                        <label for="street_name">
                            <i class="fas fa-road"></i> Street Address
                        </label>
                        <input type="text" id="street_name" name="street_name" class="form-control"
                               placeholder="House number, street name"
                               value="{{ form.street_name.value|default:'' }}" required>
                    </div>

                    <div class="form-group">
                        <label for="place_name">
                            <i class="fas fa-map-pin"></i> Area/Locality
                        </label>
                        <input type="text" id="place_name" name="place_name" class="form-control"
                               placeholder="Area, locality, landmark"
                               value="{{ form.place_name.value|default:'' }}" required>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="city">
                                <i class="fas fa-city"></i> City
                            </label>
                            <input type="text" id="city" name="city" class="form-control"
                                   placeholder="City name"
                                   value="{{ form.city.value|default:'' }}" required>
                        </div>

                        <div class="form-group">
                            <label for="state">
                                <i class="fas fa-flag"></i> State
                            </label>
                            <input type="text" id="state" name="state" class="form-control"
                                   placeholder="State name"
                                   value="{{ form.state.value|default:'' }}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="pin_code">
                            <i class="fas fa-mail-bulk"></i> PIN Code
                        </label>
                        <input type="text" id="pin_code" name="pin_code" class="form-control"
                               placeholder="6-digit PIN code" pattern="[0-9]{6}" maxlength="6"
                               value="{{ form.pin_code.value|default:'' }}" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">
                        <i class="fas fa-sticky-note"></i> Additional Address Info (Optional)
                    </label>
                    <textarea id="address" name="address" class="form-control"
                              placeholder="Any additional delivery instructions or address details" rows="3">{{ form.address.value|default:'' }}</textarea>
                    <small style="color: #6c757d; font-size: 0.85rem;">
                        <i class="fas fa-info-circle"></i> Optional: Add any special delivery instructions or additional address details
                    </small>
                </div>

                <!-- Product and quantity are now always hidden since order page requires pre-filled data -->

                <!-- Price is already calculated and shown in the product info section above -->



                <div class="text-center">
                    <button type="submit" class="btn btn-success" style="padding: 1rem 2rem; font-size: 1.1rem;">
                        <i class="fas fa-shopping-cart"></i> Proceed to Payment
                    </button>
                </div>
            </form>
        </div>

        <!-- Contact Support Section -->
        <div style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%); padding: 2rem; border-radius: 15px; margin: 2rem 0; text-align: center; border-left: 4px solid #25d366;">
            <h3 style="color: #0c4a6e; margin-bottom: 1rem;">
                <i class="fab fa-whatsapp" style="color: #25d366;"></i> Need Help with Your Order?
            </h3>
            <p style="color: #374151; margin-bottom: 1.5rem;">
                Have questions about products, pricing, or delivery? Contact us directly on WhatsApp for instant support!
            </p>
            <a href="https://wa.me/916369477095?text=Hi! I need help with placing an order for dry fish from JP Dry Fish, Gudiyattam."
               class="btn"
               style="background: #25d366; color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; display: inline-flex; align-items: center; gap: 0.5rem; font-weight: 600; transition: all 0.3s ease;"
               target="_blank"
               onmouseover="this.style.background='#128c7e'; this.style.transform='scale(1.05)'"
               onmouseout="this.style.background='#25d366'; this.style.transform='scale(1)'">
                <i class="fab fa-whatsapp" style="font-size: 1.2em;"></i>
                Chat with JP Dry Fish
            </a>
            <div style="margin-top: 1rem; color: #6b7280; font-size: 0.9rem;">
                <i class="fas fa-phone"></i> +91 6369477095 |
                <i class="fas fa-map-marker-alt"></i> Gudiyattam, Tamil Nadu
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center" style="margin: 2rem 0;">
            <a href="/" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/916369477095" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // No dynamic pricing needed since product and quantity are pre-filled



        // Form validation enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            const mobile = document.getElementById('mobile').value;
            const mobilePattern = /^[6-9]\d{9}$/;

            if (!mobilePattern.test(mobile)) {
                e.preventDefault();
                alert('Please enter a valid 10-digit mobile number starting with 6, 7, 8, or 9');
                return false;
            }
        });
    </script>
</body>
</html>
