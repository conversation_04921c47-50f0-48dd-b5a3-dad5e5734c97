# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2024-07-23 14:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (Simplified) <https://hosted.weblate.org/projects/"
"allauth/django-allauth/zh_Hans/>\n"
"Language: zh_Hans\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.7-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "此账号当前未激活。"

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "您不能删除您的主Email地址 (%(email)s)。"

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "此Email地址已关联到这个账号。"

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "您提供的Email地址或密码不正确。"

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "您提供的用户名或密码不正确。"

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "此email地址已被其他用户注册。"

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "请输入您的当前密码。"

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "错误代码。"

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "无效密码。"

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "令牌无效或过期。"

#: account/adapter.py:73
#, fuzzy
#| msgid "Invalid token."
msgid "Invalid login."
msgstr "令牌无效。"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "您重置密码的token是无效的。"

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "您不能添加超过%d个Email地址。"

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "此email地址已被其他用户注册。"

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "登录失败次数过多，请稍后重试。"

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "此email地址未分配给任何用户账号"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "此email地址未分配给任何用户账号"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "您的主Email地址必须被验证。"

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "此用户名不能使用，请改用其他用户名。"

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "您提供的用户名或密码不正确。"

#: account/adapter.py:92
msgid "Please select only one."
msgstr ""

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "使用您的密码"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "请使用认证APP或代码"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "使用安全密钥"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "将选定的email地址标注为已验证"

#: account/apps.py:11
msgid "Accounts"
msgstr "账户"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "每次输入的密码必须相同。"

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "密码"

#: account/forms.py:100
msgid "Remember Me"
msgstr "记住我"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Email地址"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "Email"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "用户名"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "登录"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "用户名或email"

#: account/forms.py:156
msgid "Username or email"
msgstr "用户名或email"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "用户名或email"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "Email （选填项）"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "忘记密码？"

#: account/forms.py:334
msgid "Email (again)"
msgstr "Email （再次输入）"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Email地址确认"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "Email （选填项）"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "Email （选填项）"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "每次输入的Email必须相同。"

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "密码（重复）"

#: account/forms.py:645
msgid "Current Password"
msgstr "当前密码"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "新密码"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "新密码（重复）"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "代码"

#: account/models.py:26
msgid "user"
msgstr "用户"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "Email地址"

#: account/models.py:34
msgid "verified"
msgstr "已验证"

#: account/models.py:35
msgid "primary"
msgstr "首选Email"

#: account/models.py:41
msgid "email addresses"
msgstr "Email地址"

#: account/models.py:151
msgid "created"
msgstr "已建立"

#: account/models.py:152
msgid "sent"
msgstr "已发送"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "密钥"

#: account/models.py:158
msgid "email confirmation"
msgstr "Email确认"

#: account/models.py:159
msgid "email confirmations"
msgstr "Email确认"

#: headless/apps.py:7
msgid "Headless"
msgstr "无头"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr "您不能在开启了两步验证的账户中添加Email地址。"

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "您不能停用两步验证。"

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr "未启用两步验证，您无法生成恢复代码。"

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr "在验证您的Email地址之前，您不能激活两步验证。"

#: mfa/adapter.py:141
msgid "Master key"
msgstr "主密钥"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "备份密钥"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "密钥 {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "多因素认证"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "恢复代码"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP 认证器"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "认证器代码"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "无密码"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"启用无密码操作允许您仅使用此密钥登录，但会提出其他要求，例如生物特征或PIN保"
"护。"

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr "已有一个账号与此Email地址关联。请先登录该账号，然后连接您的 %s 账号。"

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "令牌无效。"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "您的账号未设置密码。"

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "您的账号下无任何验证过的email地址。"

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr "您不能断开您剩下的唯一的第三方账号。"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "该第三方账号已连接到另一个账户。"

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "社交账户"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "提供者"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "提供者ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "名字"

#: socialaccount/models.py:58
msgid "client id"
msgstr "客户端ID"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "应用ID或消费者密钥"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "密钥"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "API密钥、客户端密钥或消费者密钥"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "密钥"

#: socialaccount/models.py:81
msgid "social application"
msgstr "社交应用"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "社交应用程序"

#: socialaccount/models.py:117
msgid "uid"
msgstr "用户ID"

#: socialaccount/models.py:119
msgid "last login"
msgstr "最后一次登录"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "加入日期"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "额外数据"

#: socialaccount/models.py:125
msgid "social account"
msgstr "社交账户"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "社交账号"

#: socialaccount/models.py:160
msgid "token"
msgstr "令牌"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) 或访问令牌 (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "令牌密钥"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) 或刷新令牌 (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "过期时间"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "社交应用令牌"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "社交应用令牌"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "无效个人资料数据"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "登录"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "取消"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "在请求token时收到无效的响应\"%s\"。响应如下：%s。"

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "从 \"%s\" 获取访问令牌时响应无效。"

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "未保存 \"%s\" 的请求令牌。"

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "未保存 \"%s\" 的访问令牌。"

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "无权访问私有资源 \"%s\"。"

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "从 \"%s\" 获取请求令牌时响应无效。"

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "账号未激活"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "此账号未激活。"

#: templates/account/base_confirm_code.html:27
#, fuzzy, python-format
#| msgid ""
#| "We’ve sent a code to %(email_link)s. The code expires shortly, so please "
#| "enter it soon."
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr "我们已发送一个代码到 %(email_link)s。该代码将很快过期，请尽快输入。"

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "确认"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "请求码"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "确认访问"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "请重新验证以保护您的账户安全。"

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "可选项"

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "Email Confirmation"
msgid "Email Verification"
msgstr "Email确认"

#: templates/account/confirm_email_verification_code.html:8
#, fuzzy
#| msgid "Enter an authenticator code:"
msgid "Enter Email Verification Code"
msgstr "输入身份验证器代码："

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "Email地址"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "登录"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "输入登录代码"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "密码重置"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "密码重置"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "重发验证Email"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "Enter an authenticator code:"
msgid "Enter Phone Verification Code"
msgstr "输入身份验证器代码："

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Email地址"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "以下Email地址已关联到您的帐号："

#: templates/account/email.html:25
msgid "Verified"
msgstr "已验证"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "未验证"

#: templates/account/email.html:34
msgid "Primary"
msgstr "首选Email"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "设置为首选"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "重发验证Email"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "移除"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "添加Email地址"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "添加Email"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "您真的想移除选定的Email地址吗？"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"您收到这封电子邮件是因为您或其他人尝试使用以下电子邮件地址注册账户：\n"
"\n"
"%(email)s\n"
"\n"
"但是，使用该电子邮件地址的账户已经存在。如果您忘记了这一点，请使用密码找回程"
"序来恢复您的账户：\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "账户已经存在"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "来自%(site_name)s的 Hello!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"感谢您使用 %(site_name)s！\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "您收到这封邮件是因为您的账户产生了以下变动："

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"如果这不是您做出的更改，请立即采取适当的安全措施。对您的账户的更改来自：\n"
"\n"
"- IP地址: %(ip)s\n"
"- 浏览器: %(user_agent)s\n"
"- 日期: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "您的电子邮件已从 %(from_email)s 更改为 %(to_email)s。"

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Email已更改"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "您的Email已经被确认。"

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Email确认"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this email because user %(user_display)s has given your "
#| "email address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"您收到此电子邮件是因为用户 %(user_display)s 使用您的电子邮件地址在 "
"%(site_domain)s 上注册了一个账户。\n"
"\n"
"若此操作正确，请访问 %(activate_url)s 进行确认"

#: templates/account/email/email_confirmation_message.txt:7
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr "您的登录代码如下所示。请在打开的浏览器窗口中输入。"

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "请确认您的Email地址"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "Email地址 %(deleted_email)s 已从您的账户中移除。"

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Email被移除了"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr "您的登录代码如下所示。请在打开的浏览器窗口中输入。"

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr "如果您没有发起这个操作，您可以安全地忽略这封邮件。"

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "登录代码"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "您的密码现已被修改。"

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "密码已更改"

#: templates/account/email/password_reset_code_message.txt:5
#, fuzzy
#| msgid ""
#| "Your sign-in code is listed below. Please enter it in your open browser "
#| "window."
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr "您的登录代码如下所示。请在打开的浏览器窗口中输入。"

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "密码重置"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"您收到此电子邮件是因为您或其他人申请了重置您的用户账户密码。\n"
"如果您没有申请重置密码，您可以安全地忽略此邮件。点击下面的链接以重置您的密"
"码。"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "作为提示，您的用户名是%(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "密码重置邮件"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "您的密码已被重置。"

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "您的密码已被设置。"

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "密码设置"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"您收到这封邮件是因为您或其他人尝试使用电子邮件 %(email)s 访问一个账户。然而，"
"我们的数据库中没有这个账户的任何记录。"

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "如果是您本人，您可以使用下面的链接注册一个账户。"

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "未知账号"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Email地址"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "当前Email"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "改为"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "您的电子邮件地址仍在等待验证。"

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "取消变更"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "改为"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "更改Email"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "确认Email地址"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"请确认<a href=\"mailto:%(email)s\">%(email)s</a>是用户%(user_display)s的电子"
"邮件地址。"

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "无法确认%(email)s，因为它已被另一个账户确认。"

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"这个电子邮件确认链接已过期或无效。请<a href=\"%(email_url)s\">发起新的电子邮"
"件确认请求</a>。"

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "如果您还没有创建账号，请先 %(link)s注册%(end_link)s 。"

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "使用 passkey 登录"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
#, fuzzy
#| msgid "Mail me a sign-in code"
msgid "Send me a sign-in code"
msgstr "邮寄登录代码给我"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "登出"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "您确定要登出吗？"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "您不能删除您的主Email地址 (%(email)s) 。"

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "确认邮件已发往 %(email)s。"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "您已确认Email地址 %(email)s。"

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "已移除Email地址 %(email)s。"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "以 %(name)s 成功登录。"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "您已登出。"

#: templates/account/messages/login_code_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "登录代码已发送至 %(email)s。"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "密码修改成功。"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "密码设置成功。"

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been mailed to %(email)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "登录代码已发送至 %(email)s。"

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "主Email地址已设置。"

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "修改密码"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "忘记密码？"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"忘记您的密码了吗？请在下面输入您的电子邮件地址，我们将发送一封电子邮件，让您"
"可以重置密码。"

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "重置我的密码"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "如在重置密码时遇到问题，请与我们联系。"

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"我们已经给您发送了一封电子邮件。如果您还没有收到，请检查您的垃圾邮件文件夹。"
"否则，如果几分钟内仍未收到，请联系我们。"

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "不正确的 token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"密码重置链接无效，可能是因为它已被使用。请申请<a "
"href=\"%(passwd_reset_url)s\">新的密码重置</a>。"

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "您的密码现已被修改。"

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "设置密码"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Change to"
msgid "Change Phone"
msgstr "改为"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current"
msgid "Current phone"
msgstr "当前"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your email address is still pending verification."
msgid "Your phone number is still pending verification."
msgstr "您的电子邮件地址仍在等待验证。"

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "输入您的密码:"

#: templates/account/request_login_code.html:12
#, fuzzy
#| msgid ""
#| "You will receive an email containing a special code for a password-free "
#| "sign-in."
msgid "You will receive a special code for a password-free sign-in."
msgstr "您将收到一封包含用于免密码登录的特殊代码的电子邮件。"

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "请求码"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "其它登录选项"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "注册"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "注册"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "已经有一个账号？ 请%(link)s登录%(end_link)s."

#: templates/account/signup.html:39
#, fuzzy
#| msgid "Sign in with a passkey"
msgid "Sign up using a passkey"
msgstr "使用 passkey 登录"

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "注册"

#: templates/account/signup_by_passkey.html:36
#, fuzzy
#| msgid "Other sign-in options"
msgid "Other options"
msgstr "其它登录选项"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "注册关闭"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "非常抱歉，当前已关闭注册。"

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "注意"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "您已以 %(user_display)s 的身份登录。"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "警告："

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"您当前未设置任何电子邮件地址。您真的应该添加一个电子邮件地址，以便接收通知，"
"重置密码等。"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "验证您的Email地址"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"我们已向您发送了一封验证邮件。请按照邮件中提供的链接完成注册流程。如果您在主"
"收件箱中看不到验证邮件，请检查您的垃圾邮件文件夹。如果几分钟内还没有收到验证"
"邮件，请联系我们。"

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"本站的这部分需要我们验证您的身份真实性。为此，我们需要您验证您的电子邮件地址"
"的所有权。"

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"我们已向您发送了一封用于验证的电子邮件。请点击邮件内的链接。如果您在主收件箱"
"中找不到该验证邮件，请检查您的垃圾邮件文件夹。否则，如果几分钟内还未收到，请"
"联系我们。"

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>注意：</strong>您仍然可以<a href=\"%(email_url)s\">更改您的电子邮件地"
"址</a>。"

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "消息："

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "菜单："

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "账号连接"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "双因素身份认证"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "会话"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr "您的帐户已受到双因素身份认证的保护。请输入身份验证器代码："

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr "已生成一组新的双因素身份认证恢复代码。"

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "已生成新的恢复代码"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "身份验证器应用已激活。"

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "身份验证器应用已激活"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "身份验证器应用已停用。"

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "身份验证器应用已停用"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "已添加新的安全密钥。"

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "已添加新的密钥"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "一个安全密钥已被移除。"

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "密钥已被移除"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "身份验证器应用"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "使用身份验证器应用进行身份验证处于活动状态。"

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "未激活身份验证器应用。"

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "停用"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "激活"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "安全密钥"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "您已添加%(count)s个安全密钥。"

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "未添加安全密钥。"

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "管理"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "添加"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "恢复代码"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] "在 %(total_count)s个恢复码中，有 %(unused_count)s 个可用。"

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "未设置恢复代码。"

#: templates/mfa/index.html:96
msgid "View"
msgstr "查看"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "下载"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "生成"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "已生成新的恢复代码集。"

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "已添加安全密钥。"

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "已移除安全密钥。"

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "输入身份验证器代码："

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "您即将为您的帐户生成一组新的恢复代码。"

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "此操作将使您现有的代码失效。"

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "确定要执行此操作吗？"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "未使用的代码"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "下载代码"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "生成新代码"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "激活身份验证器应用"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"为使用双因素验证保护您的帐户，您需要使用身份验证器应用扫描下方的二维码。然"
"后，在下方输入应用生成的验证代码。"

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "身份验证器密钥"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr "您可以保存此密钥，以便以后重新安装您的身份验证器应用程序。"

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "停用身份验证器应用"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr "您即将停用基于身份验证器应用的身份验证。确定要执行此操作吗？"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "添加安全密钥"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "移除安全密钥"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "您确定要移除此安全密钥吗？"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "用途"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Passkey"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "安全密钥"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "此密钥未表明其是否为 passkey。"

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "未指定"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "添加于 %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "上次使用：%(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "编辑"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "编辑安全密钥"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "保存"

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Passkey"
msgid "Create Passkey"
msgstr "Passkey"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "已建立"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "此功能需要 JavaScript。"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "第三方登录失败"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "尝试通过您的第三方账户登录时发生错误。"

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "您可以使用以下任一第三方账号登录您的账户："

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "您当前没有与此账户关联的第三方账号。"

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "添加一个第三方账号"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "已连接来自 %(provider)s 的第三方账号到您的账户。"

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "第三方账号已连接"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "已从您的账户断开来自 %(provider)s 的第三方账号连接。"

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "第三方账号已断开"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "连接 %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "您即将连接一个新的来自 %(provider)s 的第三方账号。"

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "通过 %(provider)s 登录"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "您即将使用来自 %(provider)s 的第三方账号登录。"

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "继续"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "登录已取消"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"您决定取消使用您的已有账号登录我们的网站。如果这是一个失误，请继续<a "
"href=\"%(login_url)s\">登录</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "第三方账号已连接。"

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "第三方账号已断开连接。"

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"您即将使用您的 %(provider_name)s 账号登录到\n"
"%(site_name)s。作为最后一步，请完成以下表单："

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "或使用第三方"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "已退出所有其他会话。"

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "开始时间"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP地址"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "浏览器"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "上次查看于"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "当前"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "注销其他会话"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "用户会话"

#: usersessions/models.py:92
msgid "session key"
msgstr "会话密钥"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "账号连接"

#~ msgid "Use security key or device"
#~ msgstr "请使用安全密钥或设备"

#~ msgid "Add Security Key or Device"
#~ msgstr "添加安全密钥或设备"

#~ msgid "Add key or device"
#~ msgstr "添加密钥或设备"

#~ msgid "Security Keys and Devices"
#~ msgstr "安全密钥和设备"

#~ msgid "You have not added any security keys/devices."
#~ msgstr "您尚未添加任何安全密钥/设备。"

#~ msgid "Edit Security Key or Device"
#~ msgstr "编辑安全密钥或设备"
