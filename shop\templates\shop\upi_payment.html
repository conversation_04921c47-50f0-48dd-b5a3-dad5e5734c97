<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UPI Payment - JPR Dry Fish </title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
            text-align: center;
        }
        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: left;
        }
        .qr-code {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #eee;
        }
        .upi-id {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-weight: bold;
            border-left: 4px solid #2196f3;
        }
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #e55a2b;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .instructions {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
            text-align: left;
        }
        .instructions ul {
            margin-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
        }
        .payment-apps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .payment-app {
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s;
        }
        .payment-app:hover {
            border-color: #ff6b35;
            background: #fff5f0;
        }
        .app-icon {
            font-size: 30px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 UPI Payment</h1>
            <p>Pay using any UPI app</p>
        </div>
        
        <div class="content">
            <!-- Order Summary -->
            <div class="order-summary">
                <h3>Order Summary</h3>
                <p><strong>Product:</strong> {{ order.get_product_display }}</p>
                <p><strong>Quantity:</strong> {{ order.quantity }} kg</p>
                <p><strong>Amount:</strong> ₹{{ order.total_amount }}</p>
                <p><strong>Transaction ID:</strong> {{ order.transaction_id }}</p>
            </div>

            <!-- UPI ID -->
            <div class="upi-id">
                <p><strong>Pay to UPI ID:</strong></p>
                <p>jawaharprasath@paytm</p>
            </div>

            <!-- Payment Apps -->
            <h3>Choose your UPI app:</h3>
            <div class="payment-apps">
                <a href="{{ upi_url }}" class="payment-app">
                    <div class="app-icon">📱</div>
                    <div>Google Pay</div>
                </a>
                <a href="{{ upi_url }}" class="payment-app">
                    <div class="app-icon">💜</div>
                    <div>PhonePe</div>
                </a>
                <a href="{{ upi_url }}" class="payment-app">
                    <div class="app-icon">💙</div>
                    <div>Paytm</div>
                </a>
                <a href="{{ upi_url }}" class="payment-app">
                    <div class="app-icon">🏦</div>
                    <div>BHIM</div>
                </a>
            </div>

            <!-- QR Code Placeholder -->
            <div class="qr-code">
                <h4>Or scan QR code:</h4>
                <div style="width: 200px; height: 200px; background: #f0f0f0; margin: 15px auto; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #666;">
                    QR Code<br>
                    <small>(Would be generated here)</small>
                </div>
            </div>

            <!-- Instructions -->
            <div class="instructions">
                <h4>📋 Payment Instructions:</h4>
                <ul>
                    <li>Click on your preferred UPI app above</li>
                    <li>Verify the amount: ₹{{ order.total_amount }}</li>
                    <li>Complete the payment in your UPI app</li>
                    <li>Take a screenshot of the payment confirmation</li>
                    <li>Click "I have paid" button below</li>
                </ul>
            </div>

            <!-- Action Buttons -->
            <div style="margin-top: 30px;">
                <a href="{% url 'payment_success' order.id %}" class="btn">I have paid ✓</a>
                <a href="{% url 'payment' order.id %}" class="btn btn-secondary">← Back to payment options</a>
            </div>

            <!-- Manual UPI Details -->
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: left;">
                <h4>Manual UPI Transfer:</h4>
                <p style="margin: 10px 0; color: #666;">If the above links don't work, manually enter these details in your UPI app:</p>
                <ul style="color: #666; margin-left: 20px;">
                    <li><strong>UPI ID:</strong> jawaharprasath@paytm</li>
                    <li><strong>Amount:</strong> ₹{{ order.total_amount }}</li>
                    <li><strong>Reference:</strong> {{ order.transaction_id }}</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
