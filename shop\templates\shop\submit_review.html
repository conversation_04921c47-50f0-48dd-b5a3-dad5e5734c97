{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if existing_review %}Update Review{% else %}Submit Review{% endif %} - {{ product_display_name }}</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .review-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .review-header {
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
        }
        .review-form {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .star-rating {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        .star-rating input[type="radio"] {
            display: none;
        }
        .star-rating label {
            font-size: 2rem;
            color: #ddd;
            cursor: pointer;
            transition: color 0.3s;
        }
        .star-rating label:hover,
        .star-rating label:hover ~ label,
        .star-rating input[type="radio"]:checked ~ label {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> JP Dry Fish
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                <li><a href="/order/">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="review-container">
        <!-- Header -->
        <div class="review-header">
            <h1>
                <i class="fas fa-star"></i> 
                {% if existing_review %}Update Your Review{% else %}Submit Review{% endif %}
            </h1>
            <p>Share your experience with {{ product_display_name }}</p>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Review Form -->
        <div class="review-form">
            <form method="post">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="id_product">Product</label>
                    {{ form.product }}
                    {% if form.product.errors %}
                        {% for error in form.product.errors %}
                            <div style="color: #dc3545; font-size: 0.875rem;">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="id_rating">Rating</label>
                    {{ form.rating }}
                    {% if form.rating.errors %}
                        {% for error in form.rating.errors %}
                            <div style="color: #dc3545; font-size: 0.875rem;">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="id_review_text">Your Review (Optional)</label>
                    {{ form.review_text }}
                    {% if form.review_text.errors %}
                        {% for error in form.review_text.errors %}
                            <div style="color: #dc3545; font-size: 0.875rem;">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                {% if form.non_field_errors %}
                    <div class="form-group">
                        {% for error in form.non_field_errors %}
                            <div style="color: #dc3545; font-weight: bold;">{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="text-center">
                    <button type="submit" class="btn btn-success" style="padding: 1rem 2rem; font-size: 1.1rem;">
                        <i class="fas fa-star"></i> 
                        {% if existing_review %}Update Review{% else %}Submit Review{% endif %}
                    </button>
                    <a href="{% url 'products' %}" class="btn btn-outline" style="margin-left: 1rem;">
                        <i class="fas fa-arrow-left"></i> Back to Products
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/916369477095" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
</body>
</html>
