# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Translators:
# guglie<PERSON>o <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: django-allauth\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-03-14 11:47+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <https://hosted.weblate.org/projects/allauth/django-"
"allauth/it/>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.8-rc\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Questo account non è attualmente attivo."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Non puoi eliminare il tuo indirizzo email principale."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Questo indirizzo email è già associato a questo account."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "L'indirizzo email e/o la password che hai usato non sono corretti."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Il numero di telefono e/o la password specificati non sono corretti."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Un altro utente si è già registrato con questo indirizzo email."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Per favore digita la tua password attuale."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Codice errato."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Password errata."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Chiave non valida o scaduta."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Accesso non valido."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Il codice per il reset della password non è valido."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Non puoi aggiungere più di %d indirizzi email."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "Un altro utente si è già registrato con questo indirizzo email."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Troppo tentativi di accesso. Riprova più tardi."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "L'indirizzo email non è assegnato a nessun account utente."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Il numero di telefono non è associato a nessun account utente."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Dobbiamo verificare il tuo indirizzo email principale."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "Questo username non può essere usato. Per favore scegline un altro."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Lo username e/o la password che hai usato non sono corretti."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Per favore, seleziona solo uno."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Il nuovo valore deve essere diverso da quello attuale."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "Usa la tua password"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Usa l'app di autenticazione o un codice"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Utilizza una chiave di sicurezza"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Contrassegna gli indirizzi email selezionati come verificati."

#: account/apps.py:11
msgid "Accounts"
msgstr "Account"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Inserisci un numero di telefono comprensivo di prefisso internazionale (es. "
"+39 per l'Italia)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Telefono"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Devi digitare la stessa password."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Password"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Ricordami"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Indirizzo email"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "email"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Username"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Login"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Nome utente, email o numero di telefono"

#: account/forms.py:156
msgid "Username or email"
msgstr "Username o email"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Nome utente o numero di telefono"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Email o numero di telefono"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Password dimenticata?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "email (di nuovo)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Conferma dell'indirizzo emai"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "email (opzionale)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Nome utente (opzionale)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Devi digitare la stessa password ogni volta."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Password (nuovamente)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Password attuale"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nuova password"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nuova password (nuovamente)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "Codice"

#: account/models.py:26
msgid "user"
msgstr "utente"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "indirizzo email"

#: account/models.py:34
msgid "verified"
msgstr "verificato"

#: account/models.py:35
msgid "primary"
msgstr "primario"

#: account/models.py:41
msgid "email addresses"
msgstr "indirizzo email"

#: account/models.py:151
msgid "created"
msgstr "creato"

#: account/models.py:152
msgid "sent"
msgstr "inviato"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "chiave"

#: account/models.py:158
msgid "email confirmation"
msgstr "email di conferma"

#: account/models.py:159
msgid "email confirmations"
msgstr "email di conferma"

#: headless/apps.py:7
msgid "Headless"
msgstr "Headless"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Non è possibile aggiungere un indirizzo email a un account protetto da "
"autenticazione a due fattori."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Non è possibile disattivare l'autenticazione a due fattori."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Non è possibile creare dei codici di recuperazione senza avere attivato "
"l'autenticazione a due fattori."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Non è possibile attivare l'autenticazione a due fattori fino a quando non "
"hai verificato il tuo indirizzo email."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Chiave principale"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Chiave di backup"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Chiave numero {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Codici di recupero"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "Autenticatore TOTP"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Codice autenticatore"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Senza password"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"L'abilitazione dell'operazione senza password ti consente di accedere "
"utilizzando solo questa chiave, ma impone requisiti aggiuntivi come l'uso di "
"dati biometrici o la protezione tramite PIN."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Esiste già un account con questo indirizzo email. Per favore entra con "
"quell'account, e successivamente connetti il tuo account %s."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Token non valido."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Il tuo account non ha ancora nessuna password."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Non hai ancora verificato il tuo indirizzo email."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Non puoi scollegare il tuo ultimo servizio di autenticazione Social Network:"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "L'account di terze parti è già collegato a un altro account."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Account"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "provider"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "ID del Provider"

#: socialaccount/models.py:56
msgid "name"
msgstr "nome"

#: socialaccount/models.py:58
msgid "client id"
msgstr "Id cliente"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "App ID, o consumer key"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Segreto API, segreto del cliente, or segreto del consumatore"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Chiave"

#: socialaccount/models.py:81
msgid "social application"
msgstr "applicazione sociale"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "applicazioni sociali"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "Ultimo accesso"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "data  iscrizione"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "dati aggiuntivi"

#: socialaccount/models.py:125
msgid "social account"
msgstr "account sociale"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "account sociali"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) o token di accesso (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token segreto"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) o token di aggiornamento (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "scade il"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token dell'applicazione social"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "token dell'applicazione social"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Dati profilo non validi"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Login"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Annulla"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Risposta non valida durante l'ottenimento del token di richiesta da \\\"%s\\"
"\". La risposta è stata: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Risposta non valida alla richiesta di un token da \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nessuna richiesta di token salvata per \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nessun token di accesso salvato per \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nessuna accesso alle risorse private a \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Risposta non valida alla richiesta di un token da \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Account non attivo"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Questo account non è attivo."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Abbiamo inviato un codice a %(recipient)s. Il codice scadrà a breve, quindi "
"inseriscilo al più presto."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Conferma"

#: templates/account/base_confirm_code.html:43
#, fuzzy
#| msgid "Request Code"
msgid "Request new code"
msgstr "Richiedi Codice"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Conferma l'accesso"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Si prega di rieseguire l'autenticazione per proteggere il tuo account."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Opzioni alternative"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Verifica Email"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Inserisci il codice di verificazione e-mail"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "indirizzo email"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Accedi"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Inserisci il codice di accesso."

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Re-imposta la Password"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Inserisci il Codice di Reimpostazione della Password"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Verifica del Numero di Telefono"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Inserisci il Codice di Verifica del Telefono"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Indirizzi email"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "I seguenti indirizzi email sono associati al tuo account:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Verificato"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Non verificato"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Principale"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Rendi Principale"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Re-invia la Verifica"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Rimuovi"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Aggiungi un Indirizzo email"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Aggiungi email"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Sei sicuro di voler rimuovere l'indirizzo email selezionato?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Stai ricevendo questa email perché tu o qualcun altro avete tentato di "
"registrare un account utilizzando l'indirizzo email:  \n"
"\n"
"%(email)s  \n"
"\n"
"Tuttavia, esiste già un account associato a questo indirizzo email. Se lo "
"hai dimenticato, utilizza la procedura di recupero password per recuperare "
"il tuo account:  \n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "L'account esiste già"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Ciao da %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Grazie per usare %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""
"Stai ricevendo questa email perché è stata apportata la seguente modifica al "
"tuo account:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Se non riconosci questa modifica, adotta immediatamente le opportune misure "
"di sicurezza. La modifica al tuo account proviene da:\n"
"\n"
"- Indirizzo IP: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Data: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "La tua email è stata cambiata da %(from_email)s a %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "E-mail modificato"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "La tua mail è stata confermata."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Manda una conferma"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"L'Utente %(user_display)s di %(site_domain)s ha registrato questo indirizzo "
"email."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Il tuo codice di verifica email è riportato di seguito. Inseriscilo nella "
"finestra del browser aperta."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Per confermare che questo è corretto, vai su %(activate_url)s."

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Conferma l'Indirizzo Email"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "L'indirizzo email %(deleted_email)s è stato rimosso dal tuo account."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "E-mail rimossa"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Il tuo codice di accesso è riportato di seguito. Inseriscilo nella finestra "
"del browser aperta."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Puoi ignorare questa email in sicurezza se non hai avviato questa azione."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "Codice di connessione"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "La tua password è stata cambiata."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Password cambiata"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Il tuo codice di reimpostazione della password è riportato di seguito. "
"Inseriscilo nella finestra del browser aperta."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Codice di Reimpostazione della Password"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Hai ricevuto questa mail perché hai richiesto la password per il tuo account "
"utente.\n"
"Se non hai richiesto tu il reset della password, ignora questa mail, "
"altrimenti clicca sul link qui sotto per fare il reset della password."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Nel caso tu lo abbia dimenticato, il tuo nome utente è %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Email per re-impostare la password "

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "La tua password è stata reimposta."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Password impostata."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Imposta la Password"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Stai ricevendo questa email perché tu, o qualcun altro, ha tentato di "
"accedere a un account con l'email %(email)s. Tuttavia, non abbiamo alcun "
"record di un account con questa email nel nostro database."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""
"Se sei stato tu, puoi registrarti per un account utilizzando il link qui "
"sotto."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Account sconosciuto"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Indirizzo email"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Email attuale"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Passare a"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "La tua email è ancora in attesa di verifica."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Annulla Modifica"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Passa a"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Cambia Email"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Conferma l'Indirizzo Email"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Conferma che <a href=\"mailto:%(email)s\">%(email)s</a> è un indirizzo email "
"per l'utente %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr ""
"Impossibile confermare %(email)s perché è già stata confermata da un account "
"diverso."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Questo link di conferma email è scaduto o non è valido. Ti chiediamo di <a "
"href=\"%(email_url)s\">ripetere la richiesta di conferma via email</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "Se non hai ancora creato un account, %(link)sRegistrati%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Accedi con una passkey"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Inviami un codice di accesso"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Disconnetti"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Sei sicuro di volerti disconnettere?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Non puoi eliminare il tuo indirizzo email principale (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Abbiamo inviato una conferma all’indirizzo %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Hai appena confermato l’indirizzo email %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Indirizzo Email rimosso %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Ti sei collegato con successo come %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Ti sei scollegato."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Un codice di accesso è stato inviato a %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Password cambiata con successo."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Password impostata correttamente."

#: templates/account/messages/phone_verification_sent.txt:2
#, fuzzy, python-format
#| msgid "A sign-in code has been sent to %(recipient)s."
msgid "A verification code has been sent to %(phone)s."
msgstr "Un codice di accesso è stato inviato a %(recipient)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Hai verificato il numero di telefono %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Indirizzo email principale definito."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Cambia la tua Password"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Password dimenticata?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Hai dimenticato la tua password? Inserisci qui sotto l'indirizzo email con "
"cui ti sei registrato, ti invieremo una mail con un link per re-impostarla."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Re-imposta la mia Password"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Se hai qualche problema a re-impostare la password, contattaci."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Ti abbiamo inviato un'email. Se non l'hai ricevuta, controlla nella cartella "
"dello spam. Altrimenti, contattaci se non la ricevi entro qualche minuto."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Token non valido"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Il link di re-impostazione della password non è valido, probabilmente è già "
"stato usato. Inoltra una <a href=\"%(passwd_reset_url)s\">nuova richiesta di "
"re-impostazione della password</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Password cambiata."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Imposta una password"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Cambia Numero di Telefono"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Numero di telefono attuale"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Il tuo numero di telefono è ancora in attesa di verifica."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Inserisci la tua password:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Riceverai un codice speciale per un accesso senza password."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Richiedi Codice"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Altre opzioni di accesso"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registrati"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Registrazione"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Hai già un account valido? %(link)sAccedi%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Registrati con una passkey"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Registrazione con una chiave di sicurezza"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Altre opzioni"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registrazioni Chiuse"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Spiacenti, le registrazioni sono per il momento sospese."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Nota"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Sei già collegato come %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Attenzione:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Non hai ancora indicato nessun indirizzo email. Devi inserirne uno se vuoi "
"ricevere notifiche, recuperare la password, ecc."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Verifica il tuo indirizzo Email"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Ti abbiamo inviato una email con un Link inserito all'interno. Per "
"completare il procedimento di verifica clicca sul Link. Contattaci se non "
"ricevi la mail entro qualche minuto."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Per utilizzare questa parte del sito dobbiamo verificare\n"
"che sei veramente chi dici di essere. Sarà sufficiente\n"
"dimostrare che hai effettivamente accesso al tuo indirizzo email. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Ti abbiamo inviato un messaggio email di verifica.\n"
"Clicca sul link contenuto nella mail.\n"
"Se non dovessi ricevere il messaggio entro qualche minuto, contattaci.\n"
"Grazie."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Nota:</strong> Puoi cambiare <a href=\"%(email_url)s\">in ogni "
"momento l'indirizzo email usato per la registrazione</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Messaggi:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Menu:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Connessioni all'account"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Autenticazione a Due Fattori"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sessioni"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Inserisci il codice dell'autenticatore: Il tuo account è protetto da "
"autenticazione a due fattori."

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"È stato generato un nuovo set di codici di recupero per l'Autenticazione a "
"Due Fattori."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Nuovi Codici di Recupero Generati"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Applicazione di autenticazione attivata."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Applicazione di autenticazione attivata"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Applicazione di autenticazione disattivata."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Applicazione di autenticazione disattivata"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "È stata aggiunta una nuova chiave di sicurezza."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Chiave di sicurezza aggiunta"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Una chiave di sicurezza è stata rimossa."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Chiave di sicurezza rimossa"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Applicazione di autenticazione"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "L'autenticazione tramite un'applicazione di autenticazione è attiva."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Un'applicazione di autenticazione non è attiva."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Disattiva"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Attiva"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Chiavi di sicurezza"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Hai aggiunto %(count)s chiave di sicurezza."
msgstr[1] "Hai aggiunto %(count)s chiavi di sicurezza."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Nessuna chiave di sicurezza è stata aggiunta."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Gestisci"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Aggiungi"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Codici di recupero"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Ci sono %(unused_count)s codici di recupero disponibili su un totale di "
"%(total_count)s."
msgstr[1] ""
"Ci sono %(unused_count)s codici di recupero disponibili su un totale di "
"%(total_count)s."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Nessun codice di recupero impostato."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Visualizza"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Scarica"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Genera"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "È stato generato un nuovo set di codici di recupero."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Chiave di sicurezza aggiunta."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Chiave di sicurezza rimossa."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Inserisci un codice dell'autenticatore:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""
"Stai per generare un nuovo set di codici di recupero per il tuo account."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Questa azione renderà invalidi i tuoi codici esistenti."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Sei sicuro?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Codici non utilizzati"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Scarica codici"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Genera nuovi codici"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Attiva l'applicazione di autenticazione"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Per proteggere il tuo account con l'autenticazione a due fattori, "
"scannerizza il codice QR qui sotto con la tua applicazione di "
"autenticazione. Successivamente, inserisci il codice di verifica generato "
"dall'applicazione qui sotto."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Segreto dell'autenticatore"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Puoi conservare questo segreto e utilizzarlo per reinstallare la tua "
"applicazione di autenticazione in un momento successivo."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Disattiva l'applicazione di autenticazione"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Stai per disattivare l'autenticazione basata sull'applicazione di "
"autenticazione. Sei sicuro?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Aggiungi Chiave di Sicurezza"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Rimuovi Chiave di Sicurezza"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Sei sicuro di voler rimuovere questa chiave di sicurezza?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Utilizzazione"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Chiave di sicurezza"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Chiave di sicurezza"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Questa chiave non indica se è una passkey."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Non specificato"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Aggiunta a %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Ultimo utilizzo: %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Modifica"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Modifica Chiave di Sicurezza"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Salva"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Crea Passkey"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Stai per creare una passkey per il tuo account. Poiché puoi aggiungere "
"ulteriori chiavi in seguito, puoi utilizzare un nome descrittivo per "
"distinguerle."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Crea"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Questa funzionalità richiede JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Errore di Accesso con Account di Terze Parti"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Si è verificato un errore durante il tentativo di accesso con il tuo account "
"di terze parti."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Puoi collegarti al tuo account utilizzando uno dei seguenti servizi di "
"autenticazione Social Network:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Attualmente non hai account di terze parti collegati a questo account."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Aggiungi un account di un Social Network"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""
"Un account di terze parti da %(provider)s è stato collegato al tuo account."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Account di Terze Parti Collegato"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""
"Un account di terze parti da %(provider)s è stato disconnesso dal tuo "
"account."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Account di Terze Parti Disconnesso"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Connetti %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Stai per collegare un nuovo account di terze parti da %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Accedi tramite %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""
"Stai per accedere utilizzando un account di terze parti da %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Continua"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Accesso annullato"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Hai deciso di cancellare l'accesso a questo sito usando uno dei tuoi account "
"attivi. Se è stato un errore, ripeti l'<a href=\"%(login_url)s\">Accesso</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "L'account di terze parti è stato collegato."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "L'account di terze parti è stato disconnesso."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Stai per usare il tuo account su %(provider_name)s per effettuare il login "
"su\n"
"%(site_name)s. Come ultima operazione ti chiediamo di riempire il form qui "
"sotto:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "O usa un servizio di terze parti"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Disconnesso da tutte le altre sessioni."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Iniziato alle"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "Indirizzo IP"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Browser"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Ultimo accesso alle"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Attuale"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Esci dalle altre sessioni"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Sessioni utente"

#: usersessions/models.py:92
msgid "session key"
msgstr "chiave di sessione"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Connessioni all'account"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "La password deve essere lunga almeno {0} caratteri."

#, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Stai ricevendo questa email perché tu o qualcun altro ha richiesto una\n"
#~ "password per il tuo account utente. Tuttavia, non abbiamo alcun record di "
#~ "un utente\n"
#~ "con l'indirizzo email %(email)s nel nostro database.\n"
#~ "\n"
#~ "Questa email può essere ignorata in modo sicuro se non hai richiesto un "
#~ "ripristino della password.\n"
#~ "\n"
#~ "Se sei stato tu, puoi registrarti per un account utilizzando il link qui "
#~ "sotto."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "I seguenti indirizzi email sono associati al tuo account:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Conferma l'Indirizzo Email"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Per favore, accedi con uno\n"
#~ "dei tuoi account social, o %(link)sregistra%(end_link)s\n"
#~ "il tuo account per %(site_name)s e accedi:"

#~ msgid "or"
#~ msgstr "o"

#~ msgid "change password"
#~ msgstr "cambia password"

#~ msgid "OpenID Sign In"
#~ msgstr "Accesso con OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Questo indirizzo email è gia associato a un altro account."

#~ msgid ""
#~ "We have sent you an email. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Ti abbiamo spedito una mail. Contattaci se non la ricevi entro qualche "
#~ "minuto."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Il login e/o la password che hai usato non sono corretti."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Gli username possono contenere solo lettere, cifre e @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Questo username è già in uso. Per favore scegline un altro."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Accedi"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "email address for user %(user_display)s."
#~ msgstr ""
#~ "Hai appena confermato che <a href=\"mailto:%(email)s\">%(email)s</a> è un "
#~ "indirizzo email valido per l'utente %(user_display)s."

#~ msgid "Thanks for using our site!"
#~ msgstr "Grazie per aver utilizzato questo Sito!"
