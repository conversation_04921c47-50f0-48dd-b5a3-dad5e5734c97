# Generated by Django 5.2.4 on 2025-07-17 17:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0003_order_payment_method_order_payment_status_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='city',
            field=models.Char<PERSON>ield(blank=True, help_text='City name', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='pin_code',
            field=models.CharField(blank=True, help_text='PIN/ZIP code', max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='place_name',
            field=models.Char<PERSON>ield(blank=True, help_text='Area/locality name', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='state',
            field=models.CharField(blank=True, help_text='State name', max_length=100, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='order',
            name='street_name',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, help_text='Street name and house number', max_length=200, null=True),
        ),
    ]
