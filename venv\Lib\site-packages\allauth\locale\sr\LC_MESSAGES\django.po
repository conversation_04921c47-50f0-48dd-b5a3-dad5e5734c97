# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-05-11 01:13+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Serbian <https://hosted.weblate.org/projects/allauth/"
"django-allauth/sr/>\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Овај налог је тренутно неактиван."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Не можете да уклоните примарну адресу е-поште."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Ова адреса е-поште је већ повезана са овим налогом."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Адреса е-поште и/или лозинка коју сте навели нису тачни."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Број телефона и/или лозинка које сте навели нису тачни."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Корисник је већ регистрован на овој адреси е-поште."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Молимо унесите тренутну лозинку."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Нетачан код."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Нетачна лозинка."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Неважећи или истекао кључ."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Неважећа пријава."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Токен за ресетовање лозинке је неважећи."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Не можете додати више од %d адреса е-поште."

#: account/adapter.py:76
msgid "A user is already registered with this phone number."
msgstr "Корисник је већ регистрован са овим бројем телефона."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Превише неуспелих покушаја пријављивања. Покушајте поново касније."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Адреса е-поште није додељена ниједном корисничком налогу."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Број телефона није додељен ниједном корисничком налогу."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Ваша примарна адреса е-поште мора бити потврђена."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Корисничко име се не може користити. Молимо користите друго корисничко име."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Корисничко име и/или лозинка коју сте навели нису тачни."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Молим изаберите само један."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Нова вредност мора да се разликује од тренутне."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr "Будите стрпљиви, шаљете превише захтева."

#: account/adapter.py:778
msgid "Use your password"
msgstr "Користите своју лозинку"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Користите аутентикатор апликацију или код за аутентификацију"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Користите безбедносни кључ"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Означите изабране адресе е-поште као верификоване"

#: account/apps.py:11
msgid "Accounts"
msgstr "Налози"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Унесите број телефона укључујући позивни број земље (нпр. +381 за Србију)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Телефон"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Сваки пут морате да унесете исту лозинку."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Лозинка"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Запамти ме"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Адреса е-поште"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "Е-пошта"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Корисничко име"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Пријавите се"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Корисничко име, е-пошта или телефон"

#: account/forms.py:156
msgid "Username or email"
msgstr "Корисничко име или е-пошта"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Корисничко име или телефон"

#: account/forms.py:160
msgid "Email or phone"
msgstr "Е-пошта или телефон"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Заборавили сте лозинку?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "Е-пошта (опет)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Потврда адресе е-поште"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "Е-пошта (опционо)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Корисничко име (опционо)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Морате унети исту адресу е-поште сваки пут."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Лозинка (поново)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Тренутна лозинка"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Нова лозинка"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Нова лозинка (поново)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "код"

#: account/models.py:26
msgid "user"
msgstr "корисник"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "адреса е-поште"

#: account/models.py:34
msgid "verified"
msgstr "проверено"

#: account/models.py:35
msgid "primary"
msgstr "примарна"

#: account/models.py:41
msgid "email addresses"
msgstr "адресе е-поште"

#: account/models.py:151
msgid "created"
msgstr "створено"

#: account/models.py:152
msgid "sent"
msgstr "послат"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "кључ"

#: account/models.py:158
msgid "email confirmation"
msgstr "потврда е-поште"

#: account/models.py:159
msgid "email confirmations"
msgstr "потврде е-поште"

#: headless/apps.py:7
msgid "Headless"
msgstr "Без главе"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Не можете додати адресу е-поште на налог заштићен двофакторском "
"аутентификацијом."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Не можете деактивирати двофакторску аутентификацију."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Не можете генерисати кодове за опоравак без омогућене двофакторске "
"аутентификације."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Не можете активирати двофакторску аутентификацију док не верификујете своју "
"адресу е-поште."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Главни кључ"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Резервни кључ"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Кључ бр. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Кодови за опоравак"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP аутентикатор"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Aутентикатор код"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Без лозинке"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Укључивање рада без лозинке омогућава вам да се пријавите користећи само "
"овај кључ, али намеће додатне захтеве као што су биометрија или заштита ПИН-"
"ом."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Већ постоји налог са овом адресом е-поште. Прво се пријавите на тај налог, а "
"затим повежите свој %s налог."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Неважећи токен."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Ваш налог нема подешену лозинку."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Ваш налог нема потврђену е-маил адресу."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Не можете да прекинете везу са последњим преосталим налогом треће стране."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Налог треће стране је већ повезан са другим налогом."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Друштвени налози"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "провидер"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "провидер ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "име"

#: socialaccount/models.py:58
msgid "client id"
msgstr "ид клијента"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ИД апликације или потрошачки кључ"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "тајни кључ"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Тајна АПИ-ја, тајна клијента или тајна потрошача"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Кључ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "друштвена апликација"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "друштвене апликације"

#: socialaccount/models.py:117
msgid "uid"
msgstr "уид"

#: socialaccount/models.py:119
msgid "last login"
msgstr "последња пријава"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "датум придруживања"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "додатни подаци"

#: socialaccount/models.py:125
msgid "social account"
msgstr "друштвени налог"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "друштвени налози"

#: socialaccount/models.py:160
msgid "token"
msgstr "токен"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) или токен приступа (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "токен тајна"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) или токен за освежавање (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "истиче у"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "токен друштвених апликација"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "токени друштвених апликација"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Невељавни подаци о профилу"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Пријавите се"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Откажи"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Неважећи одговор при добијању токена за захтев од %s. Одговор је био: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Неважећи одговор при добијању токена за приступ од \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Нема сачуваних токена за захтев од \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Нема сачуваних токена за приступ од \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Нема приступа приватним ресурсима у \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Неважећи одговор при добијању токена за захтев од \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Налог је неактиван"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Овај налог је неактиван."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Послали смо код на %(recipient)s. Код унесите ускоро јер важи кратко време."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Потврди"

#: templates/account/base_confirm_code.html:43
msgid "Request new code"
msgstr "Захтевај нови код"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Потврди приступ"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Поново потврдите аутентичност да бисте заштитили свој налог."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Алтернативне опције"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Потврда е-поште"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Унесите потврдни код е-поште"

#: templates/account/confirm_email_verification_code.html:16
msgid "Use a different email address"
msgstr "Користи другу адресу е-поште"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Пријавите се"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Унесите код за пријаву"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Ресетовање лозинке"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Унесите код за ресетовање лозинке"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Потврда телефона"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Унесите потврдни код за телефон"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr "Користи други број телефона"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Адресе е-поште"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "С вашим налогом су повезане следеће адресе е-пошти:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Потврђено"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Непотврђени"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Примарни"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Постави за примарни"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Поново пошаљи потврду"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Уклони"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Додај адресу е-поште"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Додај е-пошту"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Да ли стварно желите да уклоните изабрану адресу е-поште?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Примили сте ову е-пошту јер сте ви или неко други покушали да се "
"региструјете за\n"
"налог користећи адресу е-поште:\n"
"\n"
"%(email)s\n"
"\n"
"Међутим налог који користи ту адресу е-поште већ постоји. У случају да сте\n"
"заборавили на ово, молим користите процедуру за ресетовање лозинке \n"
"за опоравак вашег налога:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Налог већ постоји"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Хвала од %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Хвала вам што користите %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "Добијате ову е-пошту јер је на вашем налогу извршена следећа промена:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Ако не препознајете ову промену, одмах предузмите одговарајуће мере "
"предострожности. Промена вашег налога потиче од:\n"
"\n"
"- ИП адреса: %(ip)s\n"
"- Прегледач: %(user_agent)s\n"
"- Датум: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Ваша адреса е-поште је промењена из %(from_email)s у %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Адреса е-поште промењена"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Ваша адреса е-поште је потврђена."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Потврда е-поште"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Примили сте ову е-поруку јер је корисник %(user_display)s дао вашу адресу е-"
"поште при регистрацији налога на %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ваш потврдни код е-поште је наведен испод. Унесите га у отворени прозор "
"претраживача."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Да бисте потврдили да је ово тачно, идите на %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Молим вас потврдите вашу адресу е-поште"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "Адреса е-поште %(deleted_email)s је уклоњена са вашег налога."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Адреса е-поште је уклоњена"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Ваш код за пријављивање је наведен испод. Унесите га у отворени прозор "
"прегледача."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Ова порука се може безбедно занемарити ако нисте ви покренули ову радњу."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "регистрациони код"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Ваша лозинка је промењена."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Лозинка је промењена"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Ваш код за ресетовање лозинке је наведен испод. Унесите га у отворени прозор "
"претраживача."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Код за ресетовање лозинке"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Примили сте ову е-поруку јер сте ви или неко други затражили ресетовање "
"лозинке за ваш кориснички налог.\n"
"Може се безбедно занемарити ако нисте захтевали ресетовање лозинке. Кликните "
"на везу испод да бисте ресетовали лозинку."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "У случају да сте заборавили, ваше корисничко име је %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "Е-пошта за ресетовање лозинке"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Ваша лозинка је ресетована."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Ваша лозинка је постављена."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Лозинка је постављена"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Примили сте ову е-пошту јер сте ви или неко други покушали да приступите "
"налогу са адресом е-поште %(email)s. Међутим, ми немамо никакву евиденцију о "
"таквом налогу у нашој бази података."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "Ако сте то били ви, можете се пријавити за налог користећи везу испод."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Непознати налог"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Адреса е-поште"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Тренутна адреса е-поште"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Промена у"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Ваша адреса е-поште још увек чека верификацију."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Откажи промену"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Промените на"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Промените адресу е-поште"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Потврда адресе е-поште"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Молим потврдите да је <a href=\"mailto:%(email)s\">%(email)s</a> адреса е-"
"поште за корисника %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Није могуће потврдити %(email)s јер га је већ потврдио други налог."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ова веза за потврду адресе е-поште је истекла или је неважећа. Молимо <a "
"href=\"%(email_url)s\">пошаљите нови захтев за потврду е-поште</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Ако још увек нисте направили налог, молимо вас прво %(link)sсе "
"региструјте%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Пријавите се помоћу приступног кључа"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Пошаљите ми код за регистрацију"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Одјава"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Јесте ли сигурни да желите да се одјавите?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Не можете да уклоните примарну адресу е-поште (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Потврдна порука је послата на адресу е-поште %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Потврдили сте %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Уклоњена адреса e-поште %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Успешно сте се пријавили као %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Одјавили сте се."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Код за регистрацију је послат на %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Лозинка је успешно промењена."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Успешно сте поставили лозинку."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr "Код за потврду је послат на %(phone)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Потврдили сте број телефона %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Примарна адреса е-поште постављена."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Промени лозинку"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Заборавили сте лозинку?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Заборавили сте лозинку? Унесите своју адресу е-поште испод, а ми ћемо вам "
"послати поруку е-поште која вам омогућава да је ресетујете."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Ресетуј моју лозинку"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Молимо вас да нас контактирате ако имате проблема са ресетовањем ваше "
"лозинке."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Послали смо вам е-пошту. Ако је нисте примили, проверите своју фасциклу са "
"нежељеном поштом. У супротном, контактирајте нас ако га не добијете за "
"неколико минута."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Лош токен"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Веза за ресетовање лозинке је била неважећа, вероватно зато што је већ "
"коришћена. Затражите <a href=\"%(passwd_reset_url)s\">ново ресетовање "
"лозинке</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Ваша лозинка је сада промењена."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Постави лозинку"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Промени телефон"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Тренутни телефон"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Ваш телефон још увек чека потврду."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Унесите своју лозинку:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Добићете посебан код за пријаву без лозинке."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Захтевај код"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Друге опције за пријављивање"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Регистрација"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Региструјте се"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Већ имате налог? Онда Вас молимо да %(link)sсе пројавите%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Региструјте се користећи приступни кључ"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Регистрација приступним кључем"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Друге опције"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Регистрација затворена"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Жао нам је, али регистрација је тренутно затворена."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Напомена"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Већ сте пријављени као %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Упозорење:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Тренутно немате подешену ниједну адресу е-поште. Заиста би требало да додате "
"адресу е-поште како бисте могли да примате обавештења, ресетујете лозинку "
"итд."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Потврдите Вашу адресу е-поште"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Послали смо вам е-пошту ради верификације. Пратите наведену везу да бисте "
"завршили процес регистрације. Ако не видите е-поруку за верификацију у "
"главном пријемном сандучету, проверите фасциклу за нежељену пошту. "
"Контактирајте нас ако не примите е-поруку за верификацију у року од неколико "
"минута."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Овај део сајта захтева од Вас да потврдите\n"
"да сте Ви заиста особа која тврдите да јесте. У ту сврху захтевамо од вас\n"
"потврдите власништво над вашом адресом е-поште. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Послали смо вам поруку е-поштом за верификацију.\n"
"Молимо кликните на везу унутар те е-поруке. Ако не видите е-поруку за "
"верификацију у главном пријемном сандучету, проверите фасциклу за нежељену "
"пошту. Иначе\n"
"контактирајте нас ако не примите е-поруку у року од неколико минута."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Напомена:</strong> и даље можете <a href=\"%(email_url)s\"> да "
"промените адресу ваше е-поште</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Поруке:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Мени:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Повезани налози"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Двофакторска аутентификација"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Сесије"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Ваш налог је заштићен двофакторском аутентификацијом. Унесите код за потврду "
"идентитета:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Генерисан је нови скуп кодова за опоравак двофакторске аутентификације."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Генерисани нови кодови за опоравак"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Аутентикатор апликација је активирана."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Апликација за аутентикацију је активирана"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Аутентикатор апликација је деактивирана."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Апликација за аутентикацију је деактивирана"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Додат је нови безбедносни кључ."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Сигурносни кључ је додат"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Безбедносни кључ је уклоњен."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Сигурносни кључ је уклоњен"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Аутентикатор апликација"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Потврда идентитета помоћу апликације за аутентификацију је активна."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Апликација за аутентификацију није активна."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Деактивирај"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Активирај"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Сигурносни кључеви"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Додали сте %(count)s безбедносни кључ."
msgstr[1] "Додали сте %(count)s безбедносна кључа."
msgstr[2] "Додали сте %(count)s безбедносних кључева."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Није додат ниједан безбедносни кључ."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Управљај"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Додај"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Кодови за опоравак"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Доступан је %(unused_count)s од укупно %(total_count)s кодова за опоравак."
msgstr[1] ""
"Доступна су %(unused_count)s од укупно %(total_count)s кодова за опоравак."
msgstr[2] ""
"Доступно је %(unused_count)s од укупно %(total_count)s кодова за опоравак."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Нису подешени кодови за опоравак."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Погледај"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Преузми"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Генериши"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Генерисан је нови скуп кодова за опоравак."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Сигурносни кључ је додат."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Сигурносни кључ је уклоњен."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Унесите код за потврду идентитета:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Управо ћете генерисати нови скуп кодова за опоравак за свој налог."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Ова радња ће поништити ваше постојеће кодове."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Јесте ли сигурни?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Неискоришћени кодови"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Преузмите кодове"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Генеришите нове кодове"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Активирајте апликацију аутентикатор"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Да заштите свој налог двофакторском аутентификацијом скенирајте следећи QR "
"код помоћу апликације за аутентификацију. Затим унесите верификациони код "
"који је генерисала апликација у наставку."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Аутентикатор тајна"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Можете да сачувате ову тајну и да је користите да поново подесите апликације "
"за аутентификацију касније."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Деактивирајте аутентикатор апликацију"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Спремате се да деактивирате аутентификацију засновану на аутентикатор "
"апликацији. Јесте ли сигурни?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Верујете овом прегледачу?"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"Ако одлучите да верујете овом прегледачу, од вас се неће тражити "
"верификациони код следећи пут када се пријавите."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Веруј %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "Не веруј"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Додај сигурносни кључ"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Уклони сигурносни кључ"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Да ли сте сигурни да желите да уклоните овај безбедносни кључ?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Употреба"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Приступни кључ"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Безбедносни кључ"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Овај кључ не показује да ли је у питању приступни кључ."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Неодређено"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Додато у %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Последње коришћено %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Измени"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Измени сигурносни кључ"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Сачувај"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Направи приступни кључ"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Управо ћете креирати приступни кључ за свој налог. Пошто касније можете да "
"додате додатне кључеве, можете користити описно име да бисте их разликовали."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Направи"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Ова функционалност захтева JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Неуспешна пријава треће стране"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Дошло је до грешке при покушају да се пријавите преко налога треће стране."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Можете се пријавити на свој налог помоћу било које од следећих налога трећих "
"страна:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Тренутно немате ниједан налог треће стране повезан са овим налогом."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Додајте налог треће стране"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Налог треће стране од %(provider)s је повезан са вашим налогом."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Налог треће стране је повезан"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Налог треће стране од %(provider)s више није повезан са вашим налогом."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Налог треће стране више није повезан"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Повежите %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Управо ћете да повежете нови налог треће стране од %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Пријавите се преко %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Управо ћете се пријавити користећи налог треће стране од %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Настави"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Пријава је отказана"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Одлучили сте да откажете пријављивање на нашу веб страницу помоћу једног од "
"ваших постојећих налога. Ако је ово грешка, молимо вас да пређете на <a "
"href=\"%(login_url)s\">и пријавите се</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Налог треће стране је повезан."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Налог треће стране више није повезан."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Управо користите свој налог код %(provider_name)s да бисте се пријавили на\n"
"%(site_name)s. Као последњи корак, молимо попуните следећи образац:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Или користите трећу страну"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Одјављен са свих осталих сесија."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Започето у"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "ИП адреса"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Претраживач"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Последње виђен у"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Тренутно"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Одјавит се из других сесија"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Корисничке сесије"

#: usersessions/models.py:92
msgid "session key"
msgstr "кључ сесије"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Повезани рачуни"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Лозинка мора бити најмање {0} знакова."

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Здраво од %(site_name)s!\n"
#~ "\n"
#~ "Примате ову е-маил поруку јер сте ви или неко други тражилилозинку за ваш "
#~ "кориснички налог.\n"
#~ "Ова порука се може игнорисати ако нисте затражили ресет лозинке. Кликните "
#~ "на линк испод да бисте поништили своју лозинку."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "С вашим налогом су повезане следеће адресе е-пошти:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Потврда адресе е-поште"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Молимо Вас да се пријавите са једним од\n"
#~ "постојећих рачуна трећих страна. Или, се <a "
#~ "href=\"%(signup_url)s\">региструјте</a>\n"
#~ "за рачун код  %(site_name)s и пријавите се доле:"

#~ msgid "or"
#~ msgstr "или"

#~ msgid "change password"
#~ msgstr "промени лозинку"

#~ msgid "OpenID Sign In"
#~ msgstr "ОпенИД Пријава"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Ова адреса е-поште је већ повезана са другим налогом."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Послали смо вам е-пошту. Молимо Вас да нас контактирате ако га не "
#~ "примитеза неколико минута."
