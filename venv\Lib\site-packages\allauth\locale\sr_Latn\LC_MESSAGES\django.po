# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-05-11 01:13+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Serbian (Latin script) <https://hosted.weblate.org/projects/"
"allauth/django-allauth/sr_Latn/>\n"
"Language: sr_Latn\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 5.12-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "Ovaj nalog je trenutno neaktivan."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "Ne možete da uklonite primarnu adresu e-pošte."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "Ova adresa e-pošte je već povezana sa ovim nalogom."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "Adresa e-pošte i/ili lozinka koju ste naveli nisu tačni."

#: account/adapter.py:66
msgid "The phone number and/or password you specified are not correct."
msgstr "Broj telefona i/ili lozinka koje ste naveli nisu tačni."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "Korisnik je već registrovan na ovoj adresi e-pošte."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "Molimo unesite trenutnu lozinku."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "Netačan kod."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "Netačna lozinka."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "Nevažeći ili istekao ključ."

#: account/adapter.py:73
msgid "Invalid login."
msgstr "Nevažeća prijava."

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "Token za resetovanje lozinke je nevažeći."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "Ne možete dodati više od %d adresa e-pošte."

#: account/adapter.py:76
msgid "A user is already registered with this phone number."
msgstr "Korisnik je već registrovan sa ovim brojem telefona."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "Previše neuspelih pokušaja prijavljivanja. Pokušajte ponovo kasnije."

#: account/adapter.py:80
msgid "The email address is not assigned to any user account."
msgstr "Adresa e-pošte nije dodeljena nijednom korisničkom nalogu."

#: account/adapter.py:81
msgid "The phone number is not assigned to any user account."
msgstr "Broj telefona nije dodeljen nijednom korisničkom nalogu."

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Vaša primarna adresa e-pošte mora biti potvrđena."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr ""
"Korisničko ime se ne može koristiti. Molimo koristite drugo korisničko ime."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "Korisničko ime i/ili lozinka koju ste naveli nisu tačni."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "Molim izaberite samo jedan."

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr "Nova vrednost mora da se razlikuje od trenutne."

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr "Budite strpljivi, šaljete previše zahteva."

#: account/adapter.py:778
msgid "Use your password"
msgstr "Koristite svoju lozinku"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "Koristite autentikator aplikaciju ili kod za autentifikaciju"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "Koristite bezbednosni ključ"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "Označite izabrane adrese e-pošte kao verifikovane"

#: account/apps.py:11
msgid "Accounts"
msgstr "Nalozi"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""
"Unesite broj telefona uključujući pozivni broj zemlje (npr. +381 za Srbiju)."

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr "Telefon"

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "Svaki put morate da unesete istu lozinku."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "Lozinka"

#: account/forms.py:100
msgid "Remember Me"
msgstr "Zapamti me"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "Adresa e-pošte"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "E-pošta"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "Korisničko ime"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "Prijavite se"

#: account/forms.py:154
msgid "Username, email or phone"
msgstr "Korisničko ime, e-pošta ili telefon"

#: account/forms.py:156
msgid "Username or email"
msgstr "Korisničko ime ili e-pošta"

#: account/forms.py:158
msgid "Username or phone"
msgstr "Korisničko ime ili telefon"

#: account/forms.py:160
msgid "Email or phone"
msgstr "E-pošta ili telefon"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "Zaboravili ste lozinku?"

#: account/forms.py:334
msgid "Email (again)"
msgstr "E-pošta (opet)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "Potvrda adrese e-pošte"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "E-pošta (opciono)"

#: account/forms.py:361
msgid "Username (optional)"
msgstr "Korisničko ime (opciono)"

#: account/forms.py:435
msgid "You must type the same email each time."
msgstr "Morate uneti istu adresu e-pošte svaki put."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "Lozinka (ponovo)"

#: account/forms.py:645
msgid "Current Password"
msgstr "Trenutna lozinka"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "Nova lozinka"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "Nova lozinka (ponovo)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "kod"

#: account/models.py:26
msgid "user"
msgstr "korisnik"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "adresa e-pošte"

#: account/models.py:34
msgid "verified"
msgstr "provereno"

#: account/models.py:35
msgid "primary"
msgstr "primarna"

#: account/models.py:41
msgid "email addresses"
msgstr "adrese e-pošte"

#: account/models.py:151
msgid "created"
msgstr "stvoreno"

#: account/models.py:152
msgid "sent"
msgstr "poslat"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "ključ"

#: account/models.py:158
msgid "email confirmation"
msgstr "potvrda e-pošte"

#: account/models.py:159
msgid "email confirmations"
msgstr "potvrde e-pošte"

#: headless/apps.py:7
msgid "Headless"
msgstr "Bez glave"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"Ne možete dodati adresu e-pošte na nalog zaštićen dvofaktorskom "
"autentifikacijom."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "Ne možete deaktivirati dvofaktorsku autentifikaciju."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""
"Ne možete generisati kodove za oporavak bez omogućene dvofaktorske "
"autentifikacije."

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""
"Ne možete aktivirati dvofaktorsku autentifikaciju dok ne verifikujete svoju "
"adresu e-pošte."

#: mfa/adapter.py:141
msgid "Master key"
msgstr "Glavni ključ"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr "Rezervni ključ"

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr "Ključ br. {number}"

#: mfa/apps.py:9
msgid "MFA"
msgstr "MFA"

#: mfa/models.py:24
msgid "Recovery codes"
msgstr "Kodovi za oporavak"

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr "TOTP autentikator"

#: mfa/models.py:26
msgid "WebAuthn"
msgstr "WebAuthn"

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr "Autentikator kod"

#: mfa/webauthn/forms.py:59
msgid "Passwordless"
msgstr "Bez lozinke"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""
"Uključivanje rada bez lozinke omogućava vam da se prijavite koristeći samo "
"ovaj ključ, ali nameće dodatne zahteve kao što su biometrija ili zaštita PIN-"
"om."

#: socialaccount/adapter.py:35
#, python-format
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Već postoji nalog sa ovom adresom e-pošte. Prvo se prijavite na taj nalog, a "
"zatim povežite svoj %s nalog."

#: socialaccount/adapter.py:39
msgid "Invalid token."
msgstr "Nevažeći token."

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "Vaš nalog nema podešenu lozinku."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "Vaš nalog nema potvrđenu e-mail adresu."

#: socialaccount/adapter.py:43
msgid "You cannot disconnect your last remaining third-party account."
msgstr ""
"Ne možete da prekinete vezu sa poslednjim preostalim nalogom treće strane."

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The third-party account is already connected to a different account."
msgstr "Nalog treće strane je već povezan sa drugim nalogom."

#: socialaccount/apps.py:9
msgid "Social Accounts"
msgstr "Društveni nalozi"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "provider"

#: socialaccount/models.py:52
msgid "provider ID"
msgstr "provider ID"

#: socialaccount/models.py:56
msgid "name"
msgstr "ime"

#: socialaccount/models.py:58
msgid "client id"
msgstr "id klijenta"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "ID aplikacije ili potrošački ključ"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "tajni ključ"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "Tajna API-ja, tajna klijenta ili tajna potrošača"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
msgid "Key"
msgstr "Ključ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "društvena aplikacija"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "društvena aplikacije"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "poslednja prijava"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "datum pridruživanja"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "dodatni podaci"

#: socialaccount/models.py:125
msgid "social account"
msgstr "društveni nalog"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "društveni nalozi"

#: socialaccount/models.py:160
msgid "token"
msgstr "token"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ili token pristupa (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token tajna"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ili token za osvežavanje (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "ističe u"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "token društvenih aplikacija"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "tokeni društvenih aplikacija"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Neveljavni podaci o profilu"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
msgid "Login"
msgstr "Prijavite se"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr "Otkaži"

#: socialaccount/providers/oauth/client.py:85
#, python-format
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr ""
"Nevažeći odgovor pri dobijanju tokena za zahtev od %s. Odgovor je bio: %s."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Nevažeći odgovor pri dobijanju tokena za pristup od \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nema sačuvanih tokena za zahtev od \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nema sačuvanih tokena za pristup od \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nema pristupa privatnim resursima u \"%s\"."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Nevažeći odgovor pri dobijanju tokena za zahtev od \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "Nalog je neaktivan"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "Ovaj nalog je neaktivan."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""
"Poslali smo kod na %(recipient)s. Kod unesite uskoro jer važi kratko vreme."

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "Potvrdi"

#: templates/account/base_confirm_code.html:43
msgid "Request new code"
msgstr "Zahtevaj novi kod"

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
msgid "Confirm Access"
msgstr "Potvrdi pristup"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr "Ponovo potvrdite autentičnost da biste zaštitili svoj nalog."

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr "Alternativne opcije"

#: templates/account/confirm_email_verification_code.html:5
msgid "Email Verification"
msgstr "Potvrda e-pošte"

#: templates/account/confirm_email_verification_code.html:8
msgid "Enter Email Verification Code"
msgstr "Unesite potvrdni kod e-pošte"

#: templates/account/confirm_email_verification_code.html:16
msgid "Use a different email address"
msgstr "Koristi drugu adresu e-pošte"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "Prijavite se"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr "Unesite kod za prijavu"

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "Resetovanje lozinke"

#: templates/account/confirm_password_reset_code.html:8
msgid "Enter Password Reset Code"
msgstr "Unesite kod za resetovanje lozinke"

#: templates/account/confirm_phone_verification_code.html:5
msgid "Phone Verification"
msgstr "Potvrda telefona"

#: templates/account/confirm_phone_verification_code.html:8
msgid "Enter Phone Verification Code"
msgstr "Unesite potvrdni kod za telefon"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr "Koristi drugi broj telefona"

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "Adrese e-pošte"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "S vašim nalogom su povezane sledeće adrese e-pošti:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "Potvrđeno"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "Nepotvrđeni"

#: templates/account/email.html:34
msgid "Primary"
msgstr "Primarni"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "Postavi za primarni"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "Ponovo pošalji potvrdu"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "Ukloni"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "Dodaj adresu e-pošte"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "Dodaj e-poštu"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "Da li stvarno želite da uklonite izabranu adresu e-pošte?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""
"Primili ste ovu e-poštu jer ste vi ili neko drugi pokušali da se "
"registrujete za\n"
"nalog koristeći adresu e-pošte:\n"
"\n"
"%(email)s\n"
"\n"
"Međutim nalog koji koristi tu adresu e-pošte već postoji. U slučaju da ste\n"
"zaboravili na ovo, molim koristite proceduru za resetovanje lozinke \n"
"za oporavak vašeg naloga:\n"
"\n"
"%(password_reset_url)s"

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr "Nalog već postoji"

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Hvala od %(site_name)s!"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Hvala vam što koristite %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr "Dobijate ovu e-poštu jer je na vašem nalogu izvršena sledeća promena:"

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""
"Ako ne prepoznajete ovu promenu, odmah preduzmite odgovarajuće mere "
"predostrožnosti. Promena vašeg naloga potiče od:\n"
"\n"
"- IP adresa: %(ip)s\n"
"- Pregledač: %(user_agent)s\n"
"- Datum: %(timestamp)s"

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr "Vaša adresa e-pošte je promenjena iz %(from_email)s u %(to_email)s."

#: templates/account/email/email_changed_subject.txt:3
msgid "Email Changed"
msgstr "Adresa e-pošte promenjena"

#: templates/account/email/email_confirm_message.txt:4
msgid "Your email has been confirmed."
msgstr "Vaša adresa e-pošte je potvrđena."

#: templates/account/email/email_confirm_subject.txt:3
msgid "Email Confirmation"
msgstr "Potvrda e-pošte"

#: templates/account/email/email_confirmation_message.txt:5
#, python-format
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"Primili ste ovu e-poruku jer je korisnik %(user_display)s dao vašu adresu e-"
"pošte pri registraciji naloga na %(site_domain)s."

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Vaš potvrdni kod e-pošte je naveden ispod. Unesite ga u otvoreni prozor "
"pretraživača."

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr "Da biste potvrdili da je ovo tačno, idite na %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Molim vas potvrdite vašu adresu e-pošte"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr "Adresa e-pošte %(deleted_email)s je uklonjena sa vašeg naloga."

#: templates/account/email/email_deleted_subject.txt:3
msgid "Email Removed"
msgstr "Adresa e-pošte je uklonjena"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""
"Vaš kod za prijavljivanje je naveden ispod. Unesite ga u otvoreni prozor "
"pregledača."

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""
"Ova poruka se može bezbedno zanemariti ako niste vi pokrenuli ovu radnju."

#: templates/account/email/login_code_subject.txt:3
msgid "Sign-In Code"
msgstr "registracioni kod"

#: templates/account/email/password_changed_message.txt:4
msgid "Your password has been changed."
msgstr "Vaša lozinka je promenjena."

#: templates/account/email/password_changed_subject.txt:3
msgid "Password Changed"
msgstr "Lozinka je promenjena"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""
"Vaš kod za resetovanje lozinke je naveden ispod. Unesite ga u otvoreni "
"prozor pretraživača."

#: templates/account/email/password_reset_code_subject.txt:3
msgid "Password Reset Code"
msgstr "Kod za resetovanje lozinke"

#: templates/account/email/password_reset_key_message.txt:4
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Primili ste ovu e-poruku jer ste vi ili neko drugi zatražili resetovanje "
"lozinke za vaš korisnički nalog.\n"
"Može se bezbedno zanemariti ako niste zahtevali resetovanje lozinke. "
"Kliknite na vezu ispod da biste resetovali lozinku."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "U slučaju da ste zaboravili, vaše korisničko ime je %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "E-pošta za resetovanje lozinke"

#: templates/account/email/password_reset_message.txt:4
msgid "Your password has been reset."
msgstr "Vaša lozinka je resetovana."

#: templates/account/email/password_set_message.txt:4
msgid "Your password has been set."
msgstr "Vaša lozinka je postavljena."

#: templates/account/email/password_set_subject.txt:3
msgid "Password Set"
msgstr "Lozinka je postavljena"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""
"Primili ste ovu e-poštu jer ste vi ili neko drugi pokušali da pristupite "
"nalogu sa adresom e-pošte %(email)s. Međutim, mi nemamo nikakvu evidenciju o "
"takvom nalogu u našoj bazi podataka."

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr "Ako ste to bili vi, možete se prijaviti za nalog koristeći vezu ispod."

#: templates/account/email/unknown_account_subject.txt:3
msgid "Unknown Account"
msgstr "Nepoznati nalog"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
msgid "Email Address"
msgstr "Adresa e-pošte"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
msgid "Current email"
msgstr "Trenutna adresa e-pošte"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr "Promena u"

#: templates/account/email_change.html:35
msgid "Your email address is still pending verification."
msgstr "Vaša adresa e-pošte još uvek čeka verifikaciju."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr "Otkaži promenu"

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
msgid "Change to"
msgstr "Promenite na"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
msgid "Change Email"
msgstr "Promenite adresu e-pošte"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Potvrda adrese e-pošte"

#: templates/account/email_confirm.html:16
#, python-format
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Molim potvrdite da je <a href=\"mailto:%(email)s\">%(email)s</a> adresa e-"
"pošte za korisnika %(user_display)s."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, python-format
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Nije moguće potvrditi %(email)s jer ga je već potvrdio drugi nalog."

#: templates/account/email_confirm.html:36
#, python-format
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Ova veza za potvrdu adrese e-pošte je istekla ili je nevažeća. Molimo <a "
"href=\"%(email_url)s\">pošaljite novi zahtev za potvrdu e-pošte</a>."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr ""
"Ako još uvek niste napravili nalog, molimo vas prvo %(link)sse "
"registrujte%(end_link)s."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr "Prijavite se pomoću pristupnog ključa"

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr "Pošaljite mi kod za registraciju"

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "Odjava"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "Jeste li sigurni da želite da se odjavite?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Ne možete da uklonite primarnu adresu e-pošte (%(email)s)."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Potvrdna poruka je poslata na adresu e-pošte %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "Potvrdili ste %(email)s."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "Uklonjena adresa e-pošte %(email)s."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Uspešno ste se prijavili kao %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Odjavili ste se."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr "Kod za registraciju je poslat na %(recipient)s."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Lozinka je uspešno promenjena."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Uspešno ste postavili lozinku."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr "Kod za potvrdu je poslat na %(phone)s."

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr "Potvrdili ste broj telefona %(phone)s."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primarna adresa e-pošte postavljena."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "Promeni lozinku"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "Zaboravili ste lozinku?"

#: templates/account/password_reset.html:14
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Zaboravili ste lozinku? Unesite svoju adresu e-pošte ispod, a mi ćemo vam "
"poslati poruku e-pošte koja vam omogućava da je resetujete."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "Resetuj moju lozinku"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr ""
"Molimo vas da nas kontaktirate ako imate problema sa resetovanjem vaše "
"lozinke."

#: templates/account/password_reset_done.html:16
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Poslali smo vam e-poštu. Ako je niste primili, proverite svoju fasciklu sa "
"neželjenom poštom. U suprotnom, kontaktirajte nas ako ga ne dobijete za "
"nekoliko minuta."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "Loš token"

#: templates/account/password_reset_from_key.html:18
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Veza za resetovanje lozinke je bila nevažeća, verovatno zato što je već "
"korišćena. Zatražite <a href=\"%(passwd_reset_url)s\">novo resetovanje "
"lozinke</a>."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "Vaša lozinka je sada promenjena."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "Postavi lozinku"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
msgid "Change Phone"
msgstr "Promeni telefon"

#: templates/account/phone_change.html:18
msgid "Current phone"
msgstr "Trenutni telefon"

#: templates/account/phone_change.html:22
msgid "Your phone number is still pending verification."
msgstr "Vaš telefon još uvek čeka potvrdu."

#: templates/account/reauthenticate.html:6
msgid "Enter your password:"
msgstr "Unesite svoju lozinku:"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr "Dobićete poseban kod za prijavu bez lozinke."

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr "Zahtevaj kod"

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr "Druge opcije za prijavljivanje"

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registracija"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "Registrujte se"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "Već imate nalog? Onda Vas molimo da %(link)sse projavite%(end_link)s."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr "Registrujte se koristeći pristupni ključ"

#: templates/account/signup_by_passkey.html:8
msgid "Passkey Sign Up"
msgstr "Registracija pristupnim ključem"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr "Druge opcije"

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "Registracija zatvorena"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "Žao nam je, ali registracija je trenutno zatvorena."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "Napomena"

#: templates/account/snippets/already_logged_in.html:7
#, python-format
msgid "You are already logged in as %(user_display)s."
msgstr "Već ste prijavljeni kao %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Upozorenje:"

#: templates/account/snippets/warn_no_email.html:3
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Trenutno nemate podešenu nijednu adresu e-pošte. Zaista bi trebalo da dodate "
"adresu e-pošte kako biste mogli da primate obaveštenja, resetujete lozinku "
"itd."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "Potvrdite Vašu adresu e-pošte"

#: templates/account/verification_sent.html:12
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Poslali smo vam e-poštu radi verifikacije. Pratite navedenu vezu da biste "
"završili proces registracije. Ako ne vidite e-poruku za verifikaciju u "
"glavnom prijemnom sandučetu, proverite fasciklu za neželjenu poštu. "
"Kontaktirajte nas ako ne primite e-poruku za verifikaciju u roku od nekoliko "
"minuta."

#: templates/account/verified_email_required.html:13
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Ovaj deo sajta zahteva od Vas da potvrdite\n"
"da ste Vi zaista osoba koja tvrdite da jeste. U tu svrhu zahtevamo od vas\n"
"potvrdite vlasništvo nad vašom adresom e-pošte. "

#: templates/account/verified_email_required.html:18
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Poslali smo vam poruku e-poštom za verifikaciju.\n"
"Molimo kliknite na vezu unutar te e-poruke. Ako ne vidite e-poruku za "
"verifikaciju u glavnom prijemnom sandučetu, proverite fasciklu za neželjenu "
"poštu. Inače\n"
"kontaktirajte nas ako ne primite e-poruku u roku od nekoliko minuta."

#: templates/account/verified_email_required.html:23
#, python-format
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Napomena:</strong> i dalje možete <a href=\"%(email_url)s\"> da "
"promenite adresu vaše e-pošte</a>."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr "Poruke:"

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr "Meni:"

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "Povezani nalozi"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr "Dvofaktorska autentifikacija"

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr "Sesije"

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""
"Vaš nalog je zaštićen dvofaktorskom autentifikacijom. Unesite kod za potvrdu "
"identiteta:"

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""
"Generisan je novi skup kodova za oporavak dvofaktorske autentifikacije."

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr "Generisani novi kodovi za oporavak"

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr "Autentikator aplikacija je aktivirana."

#: templates/mfa/email/totp_activated_subject.txt:3
msgid "Authenticator App Activated"
msgstr "Aplikacija za autentikaciju je aktivirana"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr "Autentikator aplikacija je deaktivirana."

#: templates/mfa/email/totp_deactivated_subject.txt:3
msgid "Authenticator App Deactivated"
msgstr "Aplikacija za autentikaciju je deaktivirana"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr "Dodat je novi bezbednosni ključ."

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr "Sigurnosni ključ je dodat"

#: templates/mfa/email/webauthn_removed_message.txt:4
msgid "A security key has been removed."
msgstr "Bezbednosni ključ je uklonjen."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr "Sigurnosni ključ je uklonjen"

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr "Autentikator aplikacija"

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr "Potvrda identiteta pomoću aplikacije za autentifikaciju je aktivna."

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr "Aplikacija za autentifikaciju nije aktivna."

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr "Deaktiviraj"

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr "Aktiviraj"

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr "Sigurnosni ključevi"

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] "Dodali ste %(count)s bezbednosni ključ."
msgstr[1] "Dodali ste %(count)s bezbednosna ključa."
msgstr[2] "Dodali ste %(count)s bezbednosnih ključeva."

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr "Nije dodat nijedan bezbednosni ključ."

#: templates/mfa/index.html:62
msgid "Manage"
msgstr "Upravljaj"

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr "Dodaj"

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr "Kodovi za oporavak"

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
"Dostupan je %(unused_count)s od ukupno %(total_count)s kodova za oporavak."
msgstr[1] ""
"Dostupna su %(unused_count)s od ukupno %(total_count)s kodova za oporavak."
msgstr[2] ""
"Dostupno je %(unused_count)s od ukupno %(total_count)s kodova za oporavak."

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr "Nisu podešeni kodovi za oporavak."

#: templates/mfa/index.html:96
msgid "View"
msgstr "Pogledaj"

#: templates/mfa/index.html:102
msgid "Download"
msgstr "Preuzmi"

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr "Generiši"

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr "Generisan je novi skup kodova za oporavak."

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr "Sigurnosni ključ je dodat."

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr "Sigurnosni ključ je uklonjen."

#: templates/mfa/reauthenticate.html:6
msgid "Enter an authenticator code:"
msgstr "Unesite kod za potvrdu identiteta:"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr "Upravo ćete generisati novi skup kodova za oporavak za svoj nalog."

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr "Ova radnja će poništiti vaše postojeće kodove."

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr "Jeste li sigurni?"

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr "Neiskorišćeni kodovi"

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr "Preuzmite kodove"

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr "Generišite nove kodove"

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr "Aktivirajte aplikaciju autentikator"

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""
"Da zaštite svoj nalog dvofaktorskom autentifikacijom skenirajte sledeći QR "
"kod pomoću aplikacije za autentifikaciju. Zatim unesite verifikacioni kod "
"koji je generisala aplikacija u nastavku."

#: templates/mfa/totp/activate_form.html:21
msgid "Authenticator secret"
msgstr "Autentikator tajna"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""
"Možete da sačuvate ovu tajnu i da je koristite da ponovo podesite aplikacije "
"za autentifikaciju kasnije."

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr "Deaktivirajte autentikator aplikaciju"

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""
"Spremate se da deaktivirate autentifikaciju zasnovanu na autentikator "
"aplikaciji. Jeste li sigurni?"

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr "Verujete ovom pregledaču?"

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""
"Ako odlučite da verujete ovom pregledaču, od vas se neće tražiti "
"verifikacioni kod sledeći put kada se prijavite."

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr "Veruj %(period)s"

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr "Ne veruj"

#: templates/mfa/webauthn/add_form.html:7
msgid "Add Security Key"
msgstr "Dodaj sigurnosni ključ"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
msgid "Remove Security Key"
msgstr "Ukloni sigurnosni ključ"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
msgid "Are you sure you want to remove this security key?"
msgstr "Da li ste sigurni da želite da uklonite ovaj bezbednosni ključ?"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr "Upotreba"

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr "Pristupni ključ"

#: templates/mfa/webauthn/authenticator_list.html:37
msgid "Security key"
msgstr "Bezbednosni ključ"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr "Ovaj ključ ne pokazuje da li je u pitanju pristupni ključ."

#: templates/mfa/webauthn/authenticator_list.html:41
msgid "Unspecified"
msgstr "Neodređeno"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr "Dodato u %(created_at)s"

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr "Poslednje korišćeno %(last_used)s"

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr "Izmeni"

#: templates/mfa/webauthn/edit_form.html:7
msgid "Edit Security Key"
msgstr "Izmeni sigurnosni ključ"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr "Sačuvaj"

#: templates/mfa/webauthn/signup_form.html:7
msgid "Create Passkey"
msgstr "Napravi pristupni ključ"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""
"Upravo ćete kreirati pristupni ključ za svoj nalog. Pošto kasnije možete da "
"dodate dodatne ključeve, možete koristiti opisno ime da biste ih razlikovali."

#: templates/mfa/webauthn/signup_form.html:21
msgid "Create"
msgstr "Napravi"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr "Ova funkcionalnost zahteva JavaScript."

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
msgid "Third-Party Login Failure"
msgstr "Neuspešna prijava treće strane"

#: templates/socialaccount/authentication_error.html:12
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr ""
"Došlo je do greške pri pokušaju da se prijavite preko naloga treće strane."

#: templates/socialaccount/connections.html:13
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr ""
"Možete se prijaviti na svoj nalog pomoću bilo koje od sledećih naloga trećih "
"strana:"

#: templates/socialaccount/connections.html:46
msgid "You currently have no third-party accounts connected to this account."
msgstr "Trenutno nemate nijedan nalog treće strane povezan sa ovim nalogom."

#: templates/socialaccount/connections.html:50
msgid "Add a Third-Party Account"
msgstr "Dodajte nalog treće strane"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr "Nalog treće strane od %(provider)s je povezan sa vašim nalogom."

#: templates/socialaccount/email/account_connected_subject.txt:3
msgid "Third-Party Account Connected"
msgstr "Nalog treće strane je povezan"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr "Nalog treće strane od %(provider)s više nije povezan sa vašim nalogom."

#: templates/socialaccount/email/account_disconnected_subject.txt:3
msgid "Third-Party Account Disconnected"
msgstr "Nalog treće strane više nije povezan"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr "Povežite %(provider)s"

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr "Upravo ćete da povežete novi nalog treće strane od %(provider)s."

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr "Prijavite se preko %(provider)s"

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr "Upravo ćete se prijaviti koristeći nalog treće strane od %(provider)s."

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr "Nastavi"

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Prijava je otkazana"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Odlučili ste da otkažete prijavljivanje na našu veb stranicu pomoću jednog "
"od vaših postojećih naloga. Ako je ovo greška, molimo vas da pređete na <a "
"href=\"%(login_url)s\">i prijavite se</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The third-party account has been connected."
msgstr "Nalog treće strane je povezan."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The third-party account has been disconnected."
msgstr "Nalog treće strane više nije povezan."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Upravo koristite svoj nalog kod %(provider_name)s da biste se prijavili na\n"
"%(site_name)s. Kao poslednji korak, molimo popunite sledeći obrazac:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr "Ili koristite treću stranu"

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr "Odjavljen sa svih ostalih sesija."

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr "Započeto u"

#: templates/usersessions/usersession_list.html:24
msgid "IP Address"
msgstr "IP adresa"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr "Pretraživač"

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr "Poslednje viđen u"

#: templates/usersessions/usersession_list.html:47
msgid "Current"
msgstr "Trenutno"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr "Odjavit se iz drugih sesija"

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr "Korisničke sesije"

#: usersessions/models.py:92
msgid "session key"
msgstr "ključ sesije"

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "Povezani računi"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "Lozinka mora biti najmanje {0} znakova."

#, fuzzy, python-format
#~| msgid ""
#~| "Hello from %(site_name)s!\n"
#~| "\n"
#~| "You're receiving this e-mail because you or someone else has requested a "
#~| "password for your user account.\n"
#~| "It can be safely ignored if you did not request a password reset. Click "
#~| "the link below to reset your password."
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "Zdravo od %(site_name)s!\n"
#~ "\n"
#~ "Primate ovu e-mail poruku jer ste vi ili neko drugi tražililozinku za vaš "
#~ "korisnički nalog.\n"
#~ "Ova poruka se može ignorisati ako niste zatražili reset lozinke. Kliknite "
#~ "na link ispod da biste poništili svoju lozinku."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "S vašim nalogom su povezane sledeće adrese e-pošti:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "Potvrda adrese e-pošte"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "Molimo Vas da se prijavite sa jednim od\n"
#~ "postojećih računa trećih strana. Ili, se <a "
#~ "href=\"%(signup_url)s\">registrujte</a>\n"
#~ "za račun kod  %(site_name)s i prijavite se dole:"

#~ msgid "or"
#~ msgstr "ili"

#~ msgid "change password"
#~ msgstr "promeni lozinku"

#~ msgid "OpenID Sign In"
#~ msgstr "OpenID Prijava"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Ova adresa e-pošte je već povezana sa drugim nalogom."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Poslali smo vam e-poštu. Molimo Vas da nas kontaktirate ako ga ne "
#~ "primiteza nekoliko minuta."
