<!DOCTYPE html>
<html>
    <head>
        <link type="text/css"
              rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
        <title>allauth.headless OpenAPI Specification | django-allauth</title>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
        <script>
        const config = {
            url: "{% url 'headless:openapi_json' %}",
            dom_id: "#swagger-ui",
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.SwaggerUIStandalonePreset
            ],
            requestInterceptor: (req) => {
                req.headers['X-CSRFToken'] = "{{csrf_token}}";
                return req;
            },
        };
        const ui = SwaggerUIBundle(config);
        </script>
    </body>
</html>
