{% load static %}
{% load shop_extras %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ product_display_name }} - JPR Dry Fish</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .product-detail-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .pricing-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .price-table {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        .price-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid #28a745;
            transition: transform 0.3s ease;
        }
        .price-item:hover {
            transform: translateY(-5px);
        }
        .quantity-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        .current-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 0.5rem;
        }
        .original-price {
            font-size: 1rem;
            color: #6c757d;
            text-decoration: line-through;
            margin-bottom: 0.5rem;
        }
        .savings {
            font-size: 0.9rem;
            color: #ffc107;
            font-weight: bold;
        }
        .quantity-selector {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> JP Dry Fish
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                <li><a href="/order/">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="product-detail-container">
        <!-- Product Header -->
        <div style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: 3rem; color: #1f2937; margin-bottom: 1rem;">
                <i class="fas fa-fish" style="color: #28a745;"></i> {{ product_display_name }}
            </h1>
            {% if star_display %}
                <div style="font-size: 1.5rem; color: #ffc107; margin-bottom: 1rem;">
                    {{ star_display }}
                    {% if average_rating %}
                        <span style="color: #6c757d; font-size: 1rem; margin-left: 0.5rem;">
                            {{ average_rating|floatformat:1 }} ({{ review_count }} review{{ review_count|pluralize }})
                        </span>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Pricing Information -->
        <div class="pricing-card">
            <h2 style="color: #1f2937; margin-bottom: 1rem; text-align: center;">
                <i class="fas fa-tag"></i> Pricing Information
            </h2>
            <p style="text-align: center; color: #6c757d; margin-bottom: 2rem;">
                Original Price: ₹{{ pricing.per_kg_rate }}/kg | Choose your quantity for the best deals!
            </p>

            <div class="price-table">
                {% for qty, price_info in sample_prices.items %}
                    <div class="price-item">
                        <div class="quantity-display">{{ price_info.quantity_display }}</div>
                        <div class="current-price">₹{{ price_info.price }}</div>
                        {% if price_info.discount > 0 %}
                            <div class="original-price">₹{{ price_info.original_price }}</div>
                            <div class="savings">
                                <i class="fas fa-star"></i> Save ₹{{ price_info.discount }} ({{ price_info.savings_percent }}%)
                            </div>
                        {% endif %}
                        <div style="margin-top: 1rem;">
                            <a href="{% url 'order' %}?product={{ product_name }}&quantity={{ qty }}"
                               class="btn btn-success" style="padding: 0.5rem 1rem; font-size: 0.9rem; width: 100%;">
                                <i class="fas fa-cart-plus"></i> Order {{ price_info.quantity_display }}
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Custom Quantity Selector -->
        <div class="quantity-selector">
            <h3 style="color: #1f2937; margin-bottom: 1rem; text-align: center;">
                <i class="fas fa-calculator"></i> Calculate Price for Any Quantity
            </h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 1rem; align-items: end;">
                <div>
                    <label for="custom-quantity" style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Quantity (100g to 10kg)</label>
                    <select id="custom-quantity" class="form-control" onchange="calculateCustomPrice()">
                        <option value="">Select quantity...</option>
                        {% generate_quantity_options as quantity_options %}
                        {% for grams, display in quantity_options %}
                            <option value="{{ grams }}">{{ display }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div id="custom-price-display" style="display: none;">
                    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 1rem; border-radius: 10px; text-align: center;">
                        <div style="font-size: 0.9rem; opacity: 0.9;">Your Price</div>
                        <div style="font-size: 1.5rem; font-weight: bold;">₹<span id="custom-price">0</span></div>
                        <div id="custom-savings" style="font-size: 0.8rem; color: #ffc107; margin-top: 0.5rem;"></div>
                    </div>
                </div>
                <div>
                    <a href="{% url 'order' %}?product={{ product_name }}&quantity=1000"
                       class="btn btn-success" style="padding: 1rem 2rem;" id="default-order-btn">
                        <i class="fas fa-shopping-cart"></i> Buy Now (1kg)
                    </a>
                    <a href="#" class="btn btn-success" style="padding: 1rem 2rem; display: none;"
                       id="custom-order-btn" onclick="orderCustomQuantity()">
                        <i class="fas fa-shopping-cart"></i> Order Selected Quantity
                    </a>
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        {% if reviews %}
            <div style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                <h3 style="color: #1f2937; margin-bottom: 1.5rem; text-align: center;">
                    <i class="fas fa-star" style="color: #ffc107;"></i> Customer Reviews
                </h3>
                {% for review in reviews %}
                    <div style="background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; border-left: 3px solid #ffc107;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-user-circle" style="font-size: 1.5rem; color: #6c757d;"></i>
                                <strong>{{ review.user.first_name|default:review.user.username }}</strong>
                            </div>
                            <div style="color: #ffc107; font-size: 1.2rem;">{{ review.get_star_display }}</div>
                        </div>
                        {% if review.review_text %}
                            <p style="color: #4b5563; line-height: 1.6; margin: 0; font-style: italic;">
                                "{{ review.review_text }}"
                            </p>
                        {% endif %}
                        <div style="color: #9ca3af; font-size: 0.875rem; margin-top: 0.5rem;">
                            {{ review.created_at|date:"M d, Y" }}
                        </div>
                    </div>
                {% endfor %}
                <div style="text-align: center; margin-top: 1.5rem;">
                    <a href="{% url 'product_reviews' product_name %}" class="btn btn-outline">
                        <i class="fas fa-eye"></i> View All Reviews
                    </a>
                </div>
            </div>
        {% endif %}

        <!-- Back to Products -->
        <div style="text-align: center; margin: 2rem 0;">
            <a href="{% url 'products' %}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/916369477095" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Custom price calculation
        function calculateCustomPrice() {
            const quantitySelect = document.getElementById('custom-quantity');
            const priceDisplay = document.getElementById('custom-price-display');
            const priceSpan = document.getElementById('custom-price');
            const savingsDiv = document.getElementById('custom-savings');
            const defaultOrderBtn = document.getElementById('default-order-btn');
            const customOrderBtn = document.getElementById('custom-order-btn');

            const quantity_grams = quantitySelect.value;

            if (quantity_grams) {
                // Make AJAX request to get price
                fetch('{% url "get_product_price" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({
                        product: '{{ product_name }}',
                        quantity_grams: quantity_grams
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        priceSpan.textContent = data.price.toFixed(2);

                        if (data.savings > 0) {
                            savingsDiv.innerHTML = `<i class="fas fa-star"></i> Save ₹${data.savings.toFixed(2)}!`;
                        } else {
                            savingsDiv.innerHTML = '';
                        }

                        priceDisplay.style.display = 'block';

                        // Show custom order button and hide default
                        defaultOrderBtn.style.display = 'none';
                        customOrderBtn.style.display = 'inline-block';
                        customOrderBtn.innerHTML = `<i class="fas fa-shopping-cart"></i> Order ${data.quantity_display}`;
                    } else {
                        console.error('Error getting price:', data.error);
                        priceDisplay.style.display = 'none';
                        showDefaultButton();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    priceDisplay.style.display = 'none';
                    showDefaultButton();
                });
            } else {
                priceDisplay.style.display = 'none';
                showDefaultButton();
            }
        }

        function showDefaultButton() {
            document.getElementById('default-order-btn').style.display = 'inline-block';
            document.getElementById('custom-order-btn').style.display = 'none';
        }

        function orderCustomQuantity() {
            const quantity_grams = document.getElementById('custom-quantity').value;
            if (quantity_grams) {
                window.location.href = `{% url 'order' %}?product={{ product_name }}&quantity=${quantity_grams}`;
            }
        }
    </script>
</body>
</html>
