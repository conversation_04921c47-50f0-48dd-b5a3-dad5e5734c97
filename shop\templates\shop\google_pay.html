<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Pay - JPR Dry Fish </title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
            text-align: center;
        }
        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: left;
        }
        .google-pay-button {
            margin: 20px 0;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        .back-link {
            margin-top: 20px;
        }
        .back-link a {
            color: #4285f4;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 Google Pay</h1>
            <p>Secure payment with Google Pay</p>
        </div>
        
        <div class="content">
            <!-- Order Summary -->
            <div class="order-summary">
                <h3>Order Summary</h3>
                <p><strong>Product:</strong> {{ order.get_product_display }}</p>
                <p><strong>Quantity:</strong> {{ order.quantity }} kg</p>
                <p><strong>Amount:</strong> ₹{{ order.total_amount }}</p>
                <p><strong>Transaction ID:</strong> {{ order.transaction_id }}</p>
            </div>

            <!-- Google Pay Button Container -->
            <div id="google-pay-button" class="google-pay-button"></div>
            
            <!-- Loading Indicator -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Processing payment...</p>
            </div>
            
            <!-- Error Message -->
            <div class="error" id="error-message"></div>
            
            <!-- Manual Payment Option -->
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                <p style="color: #666; margin-bottom: 15px;">Having trouble with Google Pay?</p>
                <a href="{% url 'payment' order.id %}" style="color: #4285f4; text-decoration: none;">← Choose different payment method</a>
            </div>
        </div>
    </div>

    <!-- Google Pay API -->
    <script async src="https://pay.google.com/gp/p/js/pay.js" onload="onGooglePayLoaded()"></script>
    
    <script>
        let paymentsClient;
        const googlePayConfig = {{ google_pay_config|safe }};
        const paymentRequest = {{ payment_request|safe }};

        function onGooglePayLoaded() {
            paymentsClient = new google.payments.api.PaymentsClient({
                environment: googlePayConfig.environment
            });

            // Check if Google Pay is available
            paymentsClient.isReadyToPay(googlePayConfig)
                .then(function(response) {
                    if (response.result) {
                        createGooglePayButton();
                    } else {
                        showError('Google Pay is not available on this device');
                    }
                })
                .catch(function(err) {
                    console.error('Error checking Google Pay availability:', err);
                    showError('Error initializing Google Pay');
                });
        }

        function createGooglePayButton() {
            const button = paymentsClient.createButton({
                onClick: onGooglePaymentButtonClicked,
                allowedPaymentMethods: googlePayConfig.allowedPaymentMethods,
                buttonColor: 'default',
                buttonType: 'pay',
                buttonSizeMode: 'fill'
            });
            
            document.getElementById('google-pay-button').appendChild(button);
        }

        function onGooglePaymentButtonClicked() {
            showLoading();
            
            const paymentDataRequest = {
                ...googlePayConfig,
                transactionInfo: {
                    totalPriceStatus: 'FINAL',
                    totalPrice: paymentRequest.amount.toString(),
                    currencyCode: paymentRequest.currency,
                    countryCode: 'IN'
                }
            };

            paymentsClient.loadPaymentData(paymentDataRequest)
                .then(function(paymentData) {
                    // Payment successful
                    processPayment(paymentData);
                })
                .catch(function(err) {
                    hideLoading();
                    if (err.statusCode === 'CANCELED') {
                        showError('Payment was cancelled');
                    } else {
                        console.error('Payment error:', err);
                        showError('Payment failed. Please try again.');
                    }
                });
        }

        function processPayment(paymentData) {
            // In a real implementation, you would send this to your server
            // For demo purposes, we'll simulate a successful payment
            
            setTimeout(function() {
                // Simulate payment processing
                window.location.href = "{% url 'payment_success' order.id %}";
            }, 2000);
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('google-pay-button').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('google-pay-button').style.display = 'block';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            hideLoading();
        }
    </script>
</body>
</html>
