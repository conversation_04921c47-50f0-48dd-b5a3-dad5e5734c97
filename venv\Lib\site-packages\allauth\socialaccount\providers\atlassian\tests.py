from django.test import TestCase

from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse

from .provider import AtlassianProvider


class AtlassianTests(OAuth2TestsMixin, TestCase):
    provider_id = AtlassianProvider.id

    def get_mocked_response(self):
        response_data = """
        {
            "account_type": "atlassian",
            "account_id": "************************445566abcabc",
            "email": "<EMAIL>",
            "email_verified": true,
            "name": "<PERSON>",
            "picture": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************445566abcabc/1234abcd-9876-54aa-33aa-1234dfsade9487ds",
            "account_status": "active",
            "nickname": "mkrystof",
            "zoneinfo": "Australia/Sydney",
            "locale": "en-US",
            "extended_profile": {
                "job_title": "Designer",
                "organization": "<EMAIL>",
                "department": "Design team",
                "location": "Sydney"
            }
        }"""
        return MockedResponse(200, response_data)

    def get_expected_to_str(self):
        return "<EMAIL>"
