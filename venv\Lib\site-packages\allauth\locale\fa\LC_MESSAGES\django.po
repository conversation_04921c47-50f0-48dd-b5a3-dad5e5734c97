# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 14:14-0500\n"
"PO-Revision-Date: 2025-03-25 13:19+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Persian <https://hosted.weblate.org/projects/allauth/django-"
"allauth/fa/>\n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.11-dev\n"

#: account/adapter.py:55
msgid "This account is currently inactive."
msgstr "اکنون این حساب غیرفعال است."

#: account/adapter.py:57
msgid "You cannot remove your primary email address."
msgstr "امکان حذف نشانه ایمیل اصلی (%(email)s) مقدور نمی باشد."

#: account/adapter.py:60
msgid "This email address is already associated with this account."
msgstr "این نشانی ایمیل ازقبل به این حساب وصل شده."

#: account/adapter.py:63
msgid "The email address and/or password you specified are not correct."
msgstr "نشانی ایمیل یا گذرواژه نادرست است."

#: account/adapter.py:66
#, fuzzy
#| msgid "The username and/or password you specified are not correct."
msgid "The phone number and/or password you specified are not correct."
msgstr "نام‌کاربری یا گذرواژه وارد شده نادرست است."

#: account/adapter.py:68
msgid "A user is already registered with this email address."
msgstr "یک کاربر ازقبل با این نشانی ایمیل ثبت شده است."

#: account/adapter.py:69
msgid "Please type your current password."
msgstr "لطفا گذرواژه کنونی‌‌ات را وارد کن."

#: account/adapter.py:70 mfa/adapter.py:40
msgid "Incorrect code."
msgstr "کد اشتباه است."

#: account/adapter.py:71
msgid "Incorrect password."
msgstr "رمز عبور اشتباه است."

#: account/adapter.py:72
msgid "Invalid or expired key."
msgstr "کلید نامعتبر است یا منقضی شده است."

#: account/adapter.py:73
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid login."
msgstr "توکن نادرست"

#: account/adapter.py:74
msgid "The password reset token was invalid."
msgstr "توکن بازنشانی گذرواژه نامعتبر است."

#: account/adapter.py:75
#, python-format
msgid "You cannot add more than %d email addresses."
msgstr "نمی توانید بیش از %d تعداد آدرس ایمیل اضافه کنید."

#: account/adapter.py:76
#, fuzzy
#| msgid "A user is already registered with this email address."
msgid "A user is already registered with this phone number."
msgstr "یک کاربر ازقبل با این نشانی ایمیل ثبت شده است."

#: account/adapter.py:78
msgid "Too many failed login attempts. Try again later."
msgstr "تعداد تلاش های ناموفق بیش از حد مجاز شده است. لطفا بعدا تلاش کنید."

#: account/adapter.py:80
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The email address is not assigned to any user account."
msgstr "این نشانی ایمیل به هیچ حساب کاربری‌ای متصل نشده"

#: account/adapter.py:81
#, fuzzy
#| msgid "The email address is not assigned to any user account"
msgid "The phone number is not assigned to any user account."
msgstr "این نشانی ایمیل به هیچ حساب کاربری‌ای متصل نشده"

#: account/adapter.py:82
#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "نشانی ایمیل اصلی‌ شما باید تایید شده باشد."

#: account/adapter.py:84
msgid "Username can not be used. Please use other username."
msgstr "نام‌کاربری قابل استفاده نیست. لطفا از نام‌کاربری دیگری استفاده کنید."

#: account/adapter.py:87
msgid "The username and/or password you specified are not correct."
msgstr "نام‌کاربری یا گذرواژه وارد شده نادرست است."

#: account/adapter.py:92
msgid "Please select only one."
msgstr "لطفا یکی را انتخواب کنید"

#: account/adapter.py:93
msgid "The new value must be different from the current one."
msgstr ""

#: account/adapter.py:94
msgid "Be patient, you are sending too many requests."
msgstr ""

#: account/adapter.py:778
msgid "Use your password"
msgstr "از گذرواژه خود استفاده کنید"

#: account/adapter.py:787
msgid "Use authenticator app or code"
msgstr "از برنامه تایید دومرحله ای یا کد استفاده کنید"

#: account/adapter.py:794 templates/mfa/authenticate.html:41
#: templates/mfa/webauthn/reauthenticate.html:15
msgid "Use a security key"
msgstr "از کلید امنیتی استفاده کنید"

#: account/admin.py:23
msgid "Mark selected email addresses as verified"
msgstr "استفاده از ایمیل های انتخابی به عنوان آدرس های تایید شده"

#: account/apps.py:11
msgid "Accounts"
msgstr "حساب‌ها"

#: account/fields.py:43
msgid "Enter a phone number including country code (e.g. +1 for the US)."
msgstr ""

#: account/fields.py:49 account/fields.py:53 account/forms.py:372
msgid "Phone"
msgstr ""

#: account/forms.py:64 account/forms.py:574
msgid "You must type the same password each time."
msgstr "هربار باید گذرواژه‌ی یکسانی را وارد کنی."

#: account/forms.py:99 account/forms.py:501 account/forms.py:664
#: account/forms.py:760
msgid "Password"
msgstr "گذرواژه"

#: account/forms.py:100
msgid "Remember Me"
msgstr "مرا به خاطر بسپار"

#: account/forms.py:111 account/forms.py:314 account/forms.py:590
#: account/forms.py:682 account/forms.py:777 account/forms.py:912
msgid "Email address"
msgstr "نشانی ایمیل"

#: account/forms.py:115 account/forms.py:346 account/forms.py:587
#: account/forms.py:677 account/forms.py:909
msgid "Email"
msgstr "ایمیل"

#: account/forms.py:118 account/forms.py:121 account/forms.py:304
#: account/forms.py:307 account/forms.py:358
msgid "Username"
msgstr "نام‌کاربری"

#: account/forms.py:135
msgctxt "field label"
msgid "Login"
msgstr "ورود"

#: account/forms.py:154
#, fuzzy
#| msgid "Username or email"
msgid "Username, email or phone"
msgstr "نام‌کاربری یا ایمیل"

#: account/forms.py:156
msgid "Username or email"
msgstr "نام‌کاربری یا ایمیل"

#: account/forms.py:158
#, fuzzy
#| msgid "Username or email"
msgid "Username or phone"
msgstr "نام‌کاربری یا ایمیل"

#: account/forms.py:160
#, fuzzy
#| msgid "Email (optional)"
msgid "Email or phone"
msgstr "ایمیل (اختیاری)"

#: account/forms.py:183
msgid "Forgot your password?"
msgstr "گذرواژه‌ات را فراموش کرده‌ای؟"

#: account/forms.py:334
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "ایمیل (دوباره)"

#: account/forms.py:339
msgid "Email address confirmation"
msgstr "تاییدیه‌ی نشانی ایمیل"

#: account/forms.py:349
msgid "Email (optional)"
msgstr "ایمیل (اختیاری)"

#: account/forms.py:361
#, fuzzy
#| msgid "Email (optional)"
msgid "Username (optional)"
msgstr "ایمیل (اختیاری)"

#: account/forms.py:435
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "هربار باید ایمیل یکسانی وارد کنی."

#: account/forms.py:508 account/forms.py:665
msgid "Password (again)"
msgstr "گذرواژه (دوباره)"

#: account/forms.py:645
msgid "Current Password"
msgstr "گذرواژه کنونی"

#: account/forms.py:647 account/forms.py:713
msgid "New Password"
msgstr "گذرواژه جدید"

#: account/forms.py:648 account/forms.py:714
msgid "New Password (again)"
msgstr "گذرواژه جدید (دوباره)"

#: account/forms.py:837 account/forms.py:839 mfa/base/forms.py:15
#: mfa/base/forms.py:17 mfa/totp/forms.py:13
msgid "Code"
msgstr "کد"

#: account/models.py:26
msgid "user"
msgstr "کاربر"

#: account/models.py:32 account/models.py:40 account/models.py:148
msgid "email address"
msgstr "نشانی ایمیل"

#: account/models.py:34
msgid "verified"
msgstr "تاییدشده"

#: account/models.py:35
msgid "primary"
msgstr "اصلی"

#: account/models.py:41
msgid "email addresses"
msgstr "نشانی‌های ایمیل"

#: account/models.py:151
msgid "created"
msgstr "ایجاد‌شده"

#: account/models.py:152
msgid "sent"
msgstr "ارسال شد"

#: account/models.py:153 socialaccount/models.py:69
msgid "key"
msgstr "کلید"

#: account/models.py:158
msgid "email confirmation"
msgstr "تاییدیه‌ی ایمیل"

#: account/models.py:159
msgid "email confirmations"
msgstr "تاییدیه‌های ایمیل"

#: headless/apps.py:7
#, fuzzy
msgid "Headless"
msgstr "بی نتیجه"

#: mfa/adapter.py:32
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""
"امکان افزودن آدرس ایمیل به حسابی که توسط تایید دومرحله ای محافظت می شود وجود "
"ندارد."

#: mfa/adapter.py:35
msgid "You cannot deactivate two-factor authentication."
msgstr "امکان غیرفعالسازی تایید دومرحله ای مقدور نمی باشد."

#: mfa/adapter.py:38
msgid ""
"You cannot generate recovery codes without having two-factor authentication "
"enabled."
msgstr ""

#: mfa/adapter.py:42
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:141
#, fuzzy
#| msgid "secret key"
msgid "Master key"
msgstr "کلید محرمانه"

#: mfa/adapter.py:143
msgid "Backup key"
msgstr ""

#: mfa/adapter.py:144
#, python-brace-format
msgid "Key nr. {number}"
msgstr ""

#: mfa/apps.py:9
msgid "MFA"
msgstr ""

#: mfa/models.py:24
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:25
msgid "TOTP Authenticator"
msgstr ""

#: mfa/models.py:26
msgid "WebAuthn"
msgstr ""

#: mfa/totp/forms.py:11
msgid "Authenticator code"
msgstr ""

#: mfa/webauthn/forms.py:59
#, fuzzy
#| msgid "Password"
msgid "Passwordless"
msgstr "گذرواژه"

#: mfa/webauthn/forms.py:62
msgid ""
"Enabling passwordless operation allows you to sign in using just this key, "
"but imposes additional requirements such as biometrics or PIN protection."
msgstr ""

#: socialaccount/adapter.py:35
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"یه حساب‌کاربری با این نشانی رایانامه وجود دارد. لطفا نخست وارد آن شو، سپس "
"حساب %s ات را بهش وصل کن."

#: socialaccount/adapter.py:39
#, fuzzy
#| msgid "Bad Token"
msgid "Invalid token."
msgstr "توکن نادرست"

#: socialaccount/adapter.py:40
msgid "Your account has no password set up."
msgstr "هیچ گذرواژه‌ای برای حساب‌ات نهاده نشده."

#: socialaccount/adapter.py:41
msgid "Your account has no verified email address."
msgstr "حساب‌ات هیچ رایانامه‌ي تایید‌شده‌ای ندارد."

#: socialaccount/adapter.py:43
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid "You cannot disconnect your last remaining third-party account."
msgstr "با هریک از حساب‌های شخص سوم زیر می‌توانی به حساب‌ات وارد شوی:"

#: socialaccount/adapter.py:46
#: templates/socialaccount/messages/account_connected_other.txt:2
#, fuzzy
#| msgid "The social account is already connected to a different account."
msgid "The third-party account is already connected to a different account."
msgstr "حساب اجتماعی‌اِ به یه حساب دیگر متصل شده."

#: socialaccount/apps.py:9
#, fuzzy
msgid "Social Accounts"
msgstr "حساب‌های اجتماعی"

#: socialaccount/models.py:44 socialaccount/models.py:97
msgid "provider"
msgstr "فراهم‌کننده"

#: socialaccount/models.py:52
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "فراهم‌کننده"

#: socialaccount/models.py:56
#, fuzzy
msgid "name"
msgstr "نام"

#: socialaccount/models.py:58
msgid "client id"
msgstr "شناسه مشتری"

#: socialaccount/models.py:60
msgid "App ID, or consumer key"
msgstr "شناسه اپ، یا کلید مصرف‌کننده"

#: socialaccount/models.py:63
msgid "secret key"
msgstr "کلید محرمانه"

#: socialaccount/models.py:66
msgid "API secret, client secret, or consumer secret"
msgstr "رمز رابک (رابط برنامه‌ی کاربردی API)، رمز مشتری، یا رمز مصرف‌کننده"

#: socialaccount/models.py:69 templates/mfa/webauthn/authenticator_list.html:18
#, fuzzy
msgid "Key"
msgstr "کلید"

#: socialaccount/models.py:81
msgid "social application"
msgstr "اپلیکیشن اجتماعی"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "اپلیکیشن‌های اجتماعی"

#: socialaccount/models.py:117
msgid "uid"
msgstr "شناسه‌کاربری"

#: socialaccount/models.py:119
msgid "last login"
msgstr "آخرین ورود"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "تاریخ پیوست‌شده"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "داده اضافی"

#: socialaccount/models.py:125
msgid "social account"
msgstr "حساب اجتماعی"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "حساب‌های اجتماعی"

#: socialaccount/models.py:160
msgid "token"
msgstr "توکن"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) یا توکن دسترسی (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "رمز توکن"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) یا توکن تازه‌سازی (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "انقضا"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "توکن اپلیکشن اجتماعی"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "توکن‌های اپلیکیشن اجتماعی"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "داده نامعتبر نمایه"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:16
#, fuzzy
#| msgctxt "field label"
#| msgid "Login"
msgid "Login"
msgstr "ورود"

#: socialaccount/providers/dummy/templates/dummy/authenticate_form.html:19
#: templates/account/base_confirm_code.html:48
#: templates/account/base_confirm_code.html:52
#: templates/account/password_reset_from_key.html:33
#: templates/mfa/authenticate.html:28 templates/mfa/authenticate.html:44
#: templates/mfa/trust.html:29 templates/mfa/webauthn/signup_form.html:26
msgid "Cancel"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "پاسخ نامعتبر هنگام دریافت توکن درخواست از \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:86
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "پاسخ نامعتبر هنگام دریافت توکن دسترسی از \"%s\""

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "توکن درخواست‌ْای برای \"%s\" ذخیره نشده."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "توکن دسترسی‌ای برای \"%s\" ذخیره نشده."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "دسترسی به منابع خصوصی \"%s\" وجود ندارد."

#: socialaccount/providers/pocket/client.py:41
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "پاسخ نامعتبر هنگام دریافت توکن درخواست از \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:9
msgid "Account Inactive"
msgstr "حساب غیرفعال"

#: templates/account/account_inactive.html:12
msgid "This account is inactive."
msgstr "این حساب‌کاربری غیرفعال است."

#: templates/account/base_confirm_code.html:27
#, python-format
msgid ""
"We've sent a code to %(recipient)s. The code expires shortly, so please "
"enter it soon."
msgstr ""

#: templates/account/base_confirm_code.html:39
#: templates/account/email_confirm.html:24
#: templates/account/reauthenticate.html:18
#: templates/mfa/reauthenticate.html:18
msgid "Confirm"
msgstr "تایید"

#: templates/account/base_confirm_code.html:43
msgid "Request new code"
msgstr ""

#: templates/account/base_reauthenticate.html:5
#: templates/account/base_reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "تایید نشانی رایانامه"

#: templates/account/base_reauthenticate.html:12
msgid "Please reauthenticate to safeguard your account."
msgstr ""

#: templates/account/base_reauthenticate.html:19
#: templates/mfa/authenticate.html:37
msgid "Alternative options"
msgstr ""

#: templates/account/confirm_email_verification_code.html:5
#, fuzzy
#| msgid "email confirmation"
msgid "Email Verification"
msgstr "تاییدیه‌ی رایانامه"

#: templates/account/confirm_email_verification_code.html:8
#, fuzzy
#| msgid "token secret"
msgid "Enter Email Verification Code"
msgstr "رمز توکن"

#: templates/account/confirm_email_verification_code.html:16
#, fuzzy
#| msgid "email address"
msgid "Use a different email address"
msgstr "نشانی ایمیل"

#: templates/account/confirm_login_code.html:5 templates/account/login.html:5
#: templates/account/login.html:9 templates/account/login.html:31
#: templates/account/request_login_code.html:5
#: templates/allauth/layouts/base.html:68 templates/mfa/authenticate.html:6
#: templates/mfa/authenticate.html:24 templates/openid/login.html:5
#: templates/openid/login.html:9 templates/openid/login.html:20
#: templates/socialaccount/login.html:5
#: templates/socialaccount/login_redirect.html:5
msgid "Sign In"
msgstr "ورود"

#: templates/account/confirm_login_code.html:8
msgid "Enter Sign-In Code"
msgstr ""

#: templates/account/confirm_password_reset_code.html:5
#: templates/account/email/password_reset_subject.txt:3
#: templates/account/password_reset.html:4
#: templates/account/password_reset.html:8
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:10
msgid "Password Reset"
msgstr "بازنشانی گذرواژه"

#: templates/account/confirm_password_reset_code.html:8
#, fuzzy
#| msgid "Password Reset"
msgid "Enter Password Reset Code"
msgstr "بازنشانی گذرواژه"

#: templates/account/confirm_phone_verification_code.html:5
#, fuzzy
#| msgid "Re-send Verification"
msgid "Phone Verification"
msgstr "بازارسال تاییدیه"

#: templates/account/confirm_phone_verification_code.html:8
#, fuzzy
#| msgid "token secret"
msgid "Enter Phone Verification Code"
msgstr "رمز توکن"

#: templates/account/confirm_phone_verification_code.html:15
msgid "Use a different phone number"
msgstr ""

#: templates/account/email.html:4 templates/account/email.html:8
msgid "Email Addresses"
msgstr "نشانی‌های رایانامه"

#: templates/account/email.html:12
msgid "The following email addresses are associated with your account:"
msgstr "نشانی‌های رایانامه زیر به حساب‌ات متصل شده‌اند:"

#: templates/account/email.html:25
msgid "Verified"
msgstr "تایید‌شده"

#: templates/account/email.html:29
msgid "Unverified"
msgstr "تایید‌نشده"

#: templates/account/email.html:34
msgid "Primary"
msgstr "اصلی"

#: templates/account/email.html:44
msgid "Make Primary"
msgstr "اصلی کردن"

#: templates/account/email.html:47 templates/account/email_change.html:37
#: templates/account/phone_change.html:24
msgid "Re-send Verification"
msgstr "بازارسال تاییدیه"

#: templates/account/email.html:50
#: templates/mfa/webauthn/authenticator_confirm_delete.html:16
#: templates/mfa/webauthn/authenticator_list.html:60
#: templates/socialaccount/connections.html:40
msgid "Remove"
msgstr "حذف"

#: templates/account/email.html:59
msgid "Add Email Address"
msgstr "افزودن نشانی رایانامه"

#: templates/account/email.html:70
msgid "Add Email"
msgstr "افزودن رایانامه"

#: templates/account/email.html:80
msgid "Do you really want to remove the selected email address?"
msgstr "واقعا می‌خواهی نشانی رایانامه‌ی انتخاب‌شده را حذف کنی؟"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"سپاسگزارت %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"سپاس‌گزاریم برای استفاده از %(site_name)s.\n"
"‏%(site_domain)s"

#: templates/account/email/base_notification.txt:5
msgid ""
"You are receiving this mail because the following change was made to your "
"account:"
msgstr ""

#: templates/account/email/base_notification.txt:10
#, python-format
msgid ""
"If you do not recognize this change then please take proper security "
"precautions immediately. The change to your account originates from:\n"
"\n"
"- IP address: %(ip)s\n"
"- Browser: %(user_agent)s\n"
"- Date: %(timestamp)s"
msgstr ""

#: templates/account/email/email_changed_message.txt:4
#, python-format
msgid "Your email has been changed from %(from_email)s to %(to_email)s."
msgstr ""

#: templates/account/email/email_changed_subject.txt:3
#, fuzzy
#| msgid "Email address"
msgid "Email Changed"
msgstr "نشانی رایانامه"

#: templates/account/email/email_confirm_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "Your email has been confirmed."
msgstr "نشانی رایانامه‌ %(email)s را تایید کرده‌ای."

#: templates/account/email/email_confirm_subject.txt:3
#, fuzzy
#| msgid "email confirmation"
msgid "Email Confirmation"
msgstr "تاییدیه‌ی رایانامه"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s at "
#| "%(site_domain)s has given yours as an e-mail address to connect their "
#| "account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s."
msgstr ""
"سلام،\n"
"\n"
"این رایانامه را دریافت کردی چون کاربر %(user_display)s نشانی رایانامه‌ات را "
"برای اتصال به حساب‌اش در %(site_name)s داده.\n"
"\n"
"برای تایید درستی این به ‌%(activate_url)s برو.\n"

#: templates/account/email/email_confirmation_message.txt:7
msgid ""
"Your email verification code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/email_confirmation_message.txt:9
#, python-format
msgid "To confirm this is correct, go to %(activate_url)s"
msgstr ""

#: templates/account/email/email_confirmation_subject.txt:3
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Please Confirm Your Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/email/email_deleted_message.txt:4
#, python-format
msgid "Email address %(deleted_email)s has been removed from your account."
msgstr ""

#: templates/account/email/email_deleted_subject.txt:3
#, fuzzy
#| msgid "Remove"
msgid "Email Removed"
msgstr "حذف"

#: templates/account/email/login_code_message.txt:5
msgid ""
"Your sign-in code is listed below. Please enter it in your open browser "
"window."
msgstr ""

#: templates/account/email/login_code_message.txt:9
#: templates/account/email/password_reset_code_message.txt:9
#: templates/account/email/unknown_account_message.txt:6
msgid "This mail can be safely ignored if you did not initiate this action."
msgstr ""

#: templates/account/email/login_code_subject.txt:3
#, fuzzy
#| msgid "Sign In"
msgid "Sign-In Code"
msgstr "ورود"

#: templates/account/email/password_changed_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been changed."
msgstr "گذرواژه‌ات اکنون تغییر کرد."

#: templates/account/email/password_changed_subject.txt:3
#, fuzzy
#| msgid "Password (again)"
msgid "Password Changed"
msgstr "گذرواژه (ازنو)"

#: templates/account/email/password_reset_code_message.txt:5
msgid ""
"Your password reset code is listed below. Please enter it in your open "
"browser window."
msgstr ""

#: templates/account/email/password_reset_code_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Reset Code"
msgstr "بازنشانی گذرواژه"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"سلام،\n"
"\n"
"این رایانامه را دریافت کرده‌ای چون برای حساب کاربری‌ات در %(site_name)s،  از "
"جانب خودت یا کسی دیگر، یه گذرواژه درخواست شده.\n"
"برای بازنشانی گذرواژه پیوند زیر را دنبال کن. وگرنه چشم‌پوشی از این هنگامی که "
"خودت هم درخواست نکردی می‌تواند امن باشد."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "در صورت فراموشی، نام‌کاربری‌ات %(username)s است."

#: templates/account/email/password_reset_key_subject.txt:3
msgid "Password Reset Email"
msgstr "رایانامه‌ی بازنشانی گذرواژه"

#: templates/account/email/password_reset_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been reset."
msgstr "گذرواژه‌ات اکنون تغییر کرد."

#: templates/account/email/password_set_message.txt:4
#, fuzzy
#| msgid "Your password is now changed."
msgid "Your password has been set."
msgstr "گذرواژه‌ات اکنون تغییر کرد."

#: templates/account/email/password_set_subject.txt:3
#, fuzzy
#| msgid "Password Reset"
msgid "Password Set"
msgstr "بازنشانی گذرواژه"

#: templates/account/email/unknown_account_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you, or someone else, tried to access "
"an account with email %(email)s. However, we do not have any record of such "
"an account in our database."
msgstr ""

#: templates/account/email/unknown_account_message.txt:8
msgid "If it was you, you can sign up for an account using the link below."
msgstr ""

#: templates/account/email/unknown_account_subject.txt:3
#, fuzzy
#| msgid "Account"
msgid "Unknown Account"
msgstr "حساب"

#: templates/account/email_change.html:5 templates/account/email_change.html:9
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "نشانی‌های رایانامه"

#: templates/account/email_change.html:21
#: templates/account/email_change.html:29
#, fuzzy
#| msgid "Current Password"
msgid "Current email"
msgstr "گذرواژه کنونی"

#: templates/account/email_change.html:31
msgid "Changing to"
msgstr ""

#: templates/account/email_change.html:35
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification."
msgstr "نشانی رایانامه‌ی اصلی‌ات باید تاییدشده باشد."

#: templates/account/email_change.html:41
msgid "Cancel Change"
msgstr ""

#: templates/account/email_change.html:49
#: templates/account/phone_change.html:32
#, fuzzy
#| msgid "Email"
msgid "Change to"
msgstr "رایانامه"

#: templates/account/email_change.html:55
#: templates/allauth/layouts/base.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "رایانامه"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/email_confirm.html:16
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"لطفا تایید کن که <a href=\"mailto:%(email)s\">%(email)s</a> یه نشانی "
"رایانامه برای کاربر %(user_display)s است."

#: templates/account/email_confirm.html:30
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "حساب اجتماعی‌اِ به یه حساب دیگر متصل شده."

#: templates/account/email_confirm.html:36
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a "
#| "href=\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a "
"href=\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"این پیوند تاییدیه رایانامه باطل‌شده ویا نامعتبر است. لطفا یه <a "
"href=\"%(email_url)s\">تاییدیه جدید رایانامه</a> درخواست کن."

#: templates/account/login.html:19
#, python-format
msgid ""
"If you have not created an account yet, then please %(link)ssign "
"up%(end_link)s first."
msgstr "اگر هنوز یه حساب نساختی، پس لطفا نخست %(link)sثبت‌نام%(end_link)s کن."

#: templates/account/login.html:42
msgid "Sign in with a passkey"
msgstr ""

#: templates/account/login.html:47 templates/account/request_login_code.html:9
msgid "Send me a sign-in code"
msgstr ""

#: templates/account/logout.html:4 templates/account/logout.html:8
#: templates/account/logout.html:21 templates/allauth/layouts/base.html:61
#: templates/usersessions/usersession_list.html:64
msgid "Sign Out"
msgstr "خروج"

#: templates/account/logout.html:11
msgid "Are you sure you want to sign out?"
msgstr "مطمئنی می‌خواهی خارج شوی؟"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "نمی‌توانی نشانی رایانامه‌ی اصلی‌ات (%(email)s) را حذف کنی."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "رایانامه‌ی تاییدیه به %(email)s فرستاده شد."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "نشانی رایانامه‌ %(email)s را تایید کرده‌ای."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "نشانی رایانامه %(email)s حذف شد."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "با %(name)s باموفقیت وارد شدی."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "خارج شده‌ای."

#: templates/account/messages/login_code_sent.txt:2
#, python-format
msgid "A sign-in code has been sent to %(recipient)s."
msgstr ""

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "گذرواژه باموفقیت تغییر کرد."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "گذرواژه باموفقیت نهاده شد."

#: templates/account/messages/phone_verification_sent.txt:2
#, python-format
msgid "A verification code has been sent to %(phone)s."
msgstr ""

#: templates/account/messages/phone_verified.txt:2
#, python-format
msgid "You have verified phone number %(phone)s."
msgstr ""

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "نشانی رانامه اصلی نهاده شد."

#: templates/account/password_change.html:4
#: templates/account/password_change.html:8
#: templates/account/password_change.html:20
#: templates/account/password_reset_from_key.html:5
#: templates/account/password_reset_from_key.html:12
#: templates/account/password_reset_from_key.html:30
#: templates/account/password_reset_from_key_done.html:5
#: templates/account/password_reset_from_key_done.html:9
#: templates/allauth/layouts/base.html:37
msgid "Change Password"
msgstr "تغییر گذرواژه"

#: templates/account/password_change.html:22
msgid "Forgot Password?"
msgstr "گذرواژه‌ات را فراموش کرده‌ای؟"

#: templates/account/password_reset.html:14
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"گذرواژه‌ات را فراموش کرده‌ای؟ نشانی رایانامه‌ات را در زیر درج کن؛ ما رایانامه‌ی "
"بازنشانی گذرواژه را برایت خواهیم فرستاد."

#: templates/account/password_reset.html:26
msgid "Reset My Password"
msgstr "درخواست بازنشانی"

#: templates/account/password_reset.html:31
msgid "Please contact us if you have any trouble resetting your password."
msgstr "اگر مشکلی در بازنشانی گذرواژه‌ات داری، لطفا با ما تماس بگیر."

#: templates/account/password_reset_done.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"ما یه رایانامه تاییدیه بهت فرستادیم؛ لطفا روی پیوند درونش کلیک کن. اگر تا "
"دقایقی دیگر دریافتش نکردی با ما تماس بگیر."

#: templates/account/password_reset_from_key.html:10
msgid "Bad Token"
msgstr "توکن نادرست"

#: templates/account/password_reset_from_key.html:18
#, fuzzy, python-format
#| msgid ""
#| "The password reset link was invalid, possibly because it has already been "
#| "used.  Please request a <a href=\"%(passwd_reset_url)s\">new password "
#| "reset</a>."
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"پیوند بازنشانی گذرواژه نادرست است؛ شاید قبلا استفاده شده. لطفا یه <a "
"class=\"alert-link\" href=\"{{ passwd_reset_url }}\">بازنشان جدید گذرواژه</"
"a>درخواست کن."

#: templates/account/password_reset_from_key_done.html:12
msgid "Your password is now changed."
msgstr "گذرواژه‌ات اکنون تغییر کرد."

#: templates/account/password_set.html:5 templates/account/password_set.html:9
#: templates/account/password_set.html:21
msgid "Set Password"
msgstr "نهادن گذرواژه"

#: templates/account/phone_change.html:5 templates/account/phone_change.html:9
#: templates/account/phone_change.html:38
#, fuzzy
#| msgid "Email"
msgid "Change Phone"
msgstr "رایانامه"

#: templates/account/phone_change.html:18
#, fuzzy
#| msgid "Current Password"
msgid "Current phone"
msgstr "گذرواژه کنونی"

#: templates/account/phone_change.html:22
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your phone number is still pending verification."
msgstr "نشانی رایانامه‌ی اصلی‌ات باید تاییدشده باشد."

#: templates/account/reauthenticate.html:6
#, fuzzy
#| msgid "Forgot Password?"
msgid "Enter your password:"
msgstr "گذرواژه‌ات را فراموش کرده‌ای؟"

#: templates/account/request_login_code.html:12
msgid "You will receive a special code for a password-free sign-in."
msgstr ""

#: templates/account/request_login_code.html:24
msgid "Request Code"
msgstr ""

#: templates/account/request_login_code.html:30
msgid "Other sign-in options"
msgstr ""

#: templates/account/signup.html:4 templates/account/signup_by_passkey.html:4
#: templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "ثبت‌نام"

#: templates/account/signup.html:8 templates/account/signup.html:30
#: templates/account/signup_by_passkey.html:29
#: templates/allauth/layouts/base.html:74 templates/socialaccount/signup.html:9
#: templates/socialaccount/signup.html:25
msgid "Sign Up"
msgstr "ثبت نام"

#: templates/account/signup.html:17 templates/account/signup_by_passkey.html:17
#, python-format
msgid "Already have an account? Then please %(link)ssign in%(end_link)s."
msgstr "ازقبل یه حساب داری؟ پس لطفا %(link)sورود%(end_link)s کن."

#: templates/account/signup.html:39
msgid "Sign up using a passkey"
msgstr ""

#: templates/account/signup_by_passkey.html:8
#, fuzzy
#| msgid "Sign Up"
msgid "Passkey Sign Up"
msgstr "ثبت نام"

#: templates/account/signup_by_passkey.html:36
msgid "Other options"
msgstr ""

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:9
msgid "Sign Up Closed"
msgstr "توقف ثبت‌نام"

#: templates/account/signup_closed.html:12
msgid "We are sorry, but the sign up is currently closed."
msgstr "متاسفیم، ولی اکنون ثبت‌نام متوقف شده."

#: templates/account/snippets/already_logged_in.html:7
msgid "Note"
msgstr "توجه"

#: templates/account/snippets/already_logged_in.html:7
#, fuzzy, python-format
#| msgid "you are already logged in as %(user_display)s."
msgid "You are already logged in as %(user_display)s."
msgstr "ازقبل با %(user_display)s وارد شده‌ای."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "هشدار:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"اکنون هیچ نشانی رایانامه‌ی نهادیده‌ای نداری. درواقع باید یه نشانی رایانامه "
"بافزایی تا بتوانی اعلان‌ها، بازنشان گذرواژه و غیره را دریافت کنی."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:9
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:9
msgid "Verify Your Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/verification_sent.html:12
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"یه رایانامه تاییدیه بهت فرستادیم. پیوند درونش را برای کامل کردن فرایند "
"ثبت‌نام دنبال کن. اگر تا چند دقیقه‌ی دیگر دریافتش نکردی، لطفا با ما تماس بگیر."

#: templates/account/verified_email_required.html:13
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"این بخش از سایت نیاز دارد تایید کنیم شما کسی که ادعا کرده‌‌ای هستی.برای همین، "
"نیازد داریم مالکیتت بر نشانی رایانامه‌ات را تایید کنی."

#: templates/account/verified_email_required.html:18
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"ما یه رایانامه تاییدیه بهت فرستادیم؛ لطفا روی پیوند درونش کلیک کن. اگر تا "
"دقایقی دیگر دریافتش نکردی با ما تماس بگیر."

#: templates/account/verified_email_required.html:23
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>توجه:</strong> هنوز می‌توانی <a href=\"%(email_url)s\">تعویض نشانی "
"رایانامه</a> کنی."

#: templates/allauth/layouts/base.html:18
msgid "Messages:"
msgstr ""

#: templates/allauth/layouts/base.html:25
msgid "Menu:"
msgstr ""

#: templates/allauth/layouts/base.html:43
#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:9
msgid "Account Connections"
msgstr "اتصال‌های حساب"

#: templates/allauth/layouts/base.html:49 templates/mfa/authenticate.html:10
#: templates/mfa/index.html:5 templates/mfa/index.html:9
msgid "Two-Factor Authentication"
msgstr ""

#: templates/allauth/layouts/base.html:55
#: templates/usersessions/usersession_list.html:6
#: templates/usersessions/usersession_list.html:10
msgid "Sessions"
msgstr ""

#: templates/mfa/authenticate.html:13
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/email/recovery_codes_generated_message.txt:4
msgid ""
"A new set of Two-Factor Authentication recovery codes has been generated."
msgstr ""

#: templates/mfa/email/recovery_codes_generated_subject.txt:3
msgid "New Recovery Codes Generated"
msgstr ""

#: templates/mfa/email/totp_activated_message.txt:4
#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/email/totp_activated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Activated"
msgstr "رمز توکن"

#: templates/mfa/email/totp_deactivated_message.txt:4
#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/email/totp_deactivated_subject.txt:3
#, fuzzy
#| msgid "token secret"
msgid "Authenticator App Deactivated"
msgstr "رمز توکن"

#: templates/mfa/email/webauthn_added_message.txt:4
msgid "A new security key has been added."
msgstr ""

#: templates/mfa/email/webauthn_added_subject.txt:3
msgid "Security Key Added"
msgstr ""

#: templates/mfa/email/webauthn_removed_message.txt:4
#, fuzzy
#| msgid "You have confirmed %(email)s."
msgid "A security key has been removed."
msgstr "نشانی رایانامه‌ %(email)s را تایید کرده‌ای."

#: templates/mfa/email/webauthn_removed_subject.txt:3
msgid "Security Key Removed"
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:19
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:23
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:32 templates/mfa/totp/deactivate_form.html:24
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:36 templates/mfa/totp/activate_form.html:32
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:45 templates/mfa/webauthn/authenticator_list.html:8
#: templates/mfa/webauthn/base.html:4
msgid "Security Keys"
msgstr ""

#: templates/mfa/index.html:50
#, python-format
msgid "You have added %(count)s security key."
msgid_plural "You have added %(count)s security keys."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:54
#: templates/mfa/webauthn/authenticator_list.html:12
msgid "No security keys have been added."
msgstr ""

#: templates/mfa/index.html:62
msgid "Manage"
msgstr ""

#: templates/mfa/index.html:67 templates/mfa/webauthn/add_form.html:18
#: templates/mfa/webauthn/authenticator_list.html:70
msgid "Add"
msgstr ""

#: templates/mfa/index.html:77 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:6
#: templates/mfa/recovery_codes/index.html:6
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:82 templates/mfa/recovery_codes/index.html:9
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:86
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/index.html:96
msgid "View"
msgstr ""

#: templates/mfa/index.html:102
msgid "Download"
msgstr ""

#: templates/mfa/index.html:110 templates/mfa/recovery_codes/generate.html:29
msgid "Generate"
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/webauthn_added.txt:2
msgid "Security key added."
msgstr ""

#: templates/mfa/messages/webauthn_removed.txt:2
msgid "Security key removed."
msgstr ""

#: templates/mfa/reauthenticate.html:6
#, fuzzy
#| msgid "token secret"
msgid "Enter an authenticator code:"
msgstr "رمز توکن"

#: templates/mfa/recovery_codes/generate.html:9
msgid "You are about to generate a new set of recovery codes for your account."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "This action will invalidate your existing codes."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:13
msgid "Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/index.html:13
msgid "Unused codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:25
msgid "Download codes"
msgstr ""

#: templates/mfa/recovery_codes/index.html:30
msgid "Generate new codes"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
#: templates/mfa/totp/activate_form.html:8
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:11
msgid ""
"To protect your account with two-factor authentication, scan the QR code "
"below with your authenticator app. Then, input the verification code "
"generated by the app below."
msgstr ""

#: templates/mfa/totp/activate_form.html:21
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "رمز توکن"

#: templates/mfa/totp/activate_form.html:24
msgid ""
"You can store this secret and use it to reinstall your authenticator app at "
"a later time."
msgstr ""

#: templates/mfa/totp/deactivate_form.html:5
#: templates/mfa/totp/deactivate_form.html:9
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:12
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/mfa/trust.html:4 templates/mfa/trust.html:8
msgid "Trust this Browser?"
msgstr ""

#: templates/mfa/trust.html:11
msgid ""
"If you choose to trust this browser, you will not be asked for a "
"verification code the next time you sign in."
msgstr ""

#: templates/mfa/trust.html:23
#, python-format
msgid "Trust for %(period)s"
msgstr ""

#: templates/mfa/trust.html:26
msgid "Don't Trust"
msgstr ""

#: templates/mfa/webauthn/add_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Add Security Key"
msgstr "کلید محرمانه"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:6
#, fuzzy
#| msgid "secret key"
msgid "Remove Security Key"
msgstr "کلید محرمانه"

#: templates/mfa/webauthn/authenticator_confirm_delete.html:9
#, fuzzy
#| msgid "Are you sure you want to sign out?"
msgid "Are you sure you want to remove this security key?"
msgstr "مطمئنی می‌خواهی خارج شوی؟"

#: templates/mfa/webauthn/authenticator_list.html:21
msgid "Usage"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:33
msgid "Passkey"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:37
#, fuzzy
#| msgid "secret key"
msgid "Security key"
msgstr "کلید محرمانه"

#: templates/mfa/webauthn/authenticator_list.html:40
msgid "This key does not indicate whether it is a passkey."
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:41
#, fuzzy
#| msgid "Unverified"
msgid "Unspecified"
msgstr "تایید‌نشده"

#: templates/mfa/webauthn/authenticator_list.html:46
#, python-format
msgid "Added on %(created_at)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:48
#, python-format
msgid "Last used %(last_used)s"
msgstr ""

#: templates/mfa/webauthn/authenticator_list.html:56
msgid "Edit"
msgstr ""

#: templates/mfa/webauthn/edit_form.html:7
#, fuzzy
#| msgid "secret key"
msgid "Edit Security Key"
msgstr "کلید محرمانه"

#: templates/mfa/webauthn/edit_form.html:18
msgid "Save"
msgstr ""

#: templates/mfa/webauthn/signup_form.html:7
#, fuzzy
#| msgid "Current Password"
msgid "Create Passkey"
msgstr "گذرواژه کنونی"

#: templates/mfa/webauthn/signup_form.html:10
msgid ""
"You are about to create a passkey for your account. As you can add "
"additional keys later on, you can use a descriptive name to tell the keys "
"apart."
msgstr ""

#: templates/mfa/webauthn/signup_form.html:21
#, fuzzy
#| msgid "created"
msgid "Create"
msgstr "ایجاد‌شده"

#: templates/mfa/webauthn/snippets/scripts.html:2
msgid "This functionality requires JavaScript."
msgstr ""

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:9
#, fuzzy
#| msgid "Social Network Login Failure"
msgid "Third-Party Login Failure"
msgstr "ناموفق در ورود با شبکه اجتماعی"

#: templates/socialaccount/authentication_error.html:12
#, fuzzy
#| msgid ""
#| "An error occurred while attempting to login via your social network "
#| "account."
msgid ""
"An error occurred while attempting to login via your third-party account."
msgstr "یه خطا هنگام سعی برای ورود با حساب شبکه‌ی اجتماعی‌ات رخ داد."

#: templates/socialaccount/connections.html:13
#, fuzzy
#| msgid ""
#| "You can sign in to your account using any of the following third party "
#| "accounts:"
msgid ""
"You can sign in to your account using any of the following third-party "
"accounts:"
msgstr "با هریک از حساب‌های شخص سوم زیر می‌توانی به حساب‌ات وارد شوی:"

#: templates/socialaccount/connections.html:46
#, fuzzy
#| msgid ""
#| "You currently have no social network accounts connected to this account."
msgid "You currently have no third-party accounts connected to this account."
msgstr "اکنون هیچ حساب شبکه‌ی اجتماعی‌ای به این حساب متصل نشده."

#: templates/socialaccount/connections.html:50
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Add a Third-Party Account"
msgstr "افزودن یه حساب سوم شخص"

#: templates/socialaccount/email/account_connected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been connected to your account."
msgstr ""

#: templates/socialaccount/email/account_connected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Connected"
msgstr "افزودن یه حساب سوم شخص"

#: templates/socialaccount/email/account_disconnected_message.txt:4
#, python-format
msgid ""
"A third-party account from %(provider)s has been disconnected from your "
"account."
msgstr ""

#: templates/socialaccount/email/account_disconnected_subject.txt:3
#, fuzzy
#| msgid "Add a 3rd Party Account"
msgid "Third-Party Account Disconnected"
msgstr "افزودن یه حساب سوم شخص"

#: templates/socialaccount/login.html:10
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:13
#, python-format
msgid "You are about to connect a new third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:17
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:20
#, python-format
msgid "You are about to sign in using a third-party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:27
#: templates/socialaccount/login_redirect.html:10
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "لغو ورود"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a "
"href=\"%(login_url)s\">sign in</a>."
msgstr ""
"ورودت به سایت‌مان با یکی از حساب‌هایت را لغو کردی. اگر اشتباهی شده، لطفا اقدام "
"به <a href=\"%(login_url)s\">ورود</a> کن."

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
#| msgid "The social account has been connected."
msgid "The third-party account has been connected."
msgstr "حساب اجتماعی‌اِ متصل شد."

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
#| msgid "The social account has been disconnected."
msgid "The third-party account has been disconnected."
msgstr "حساب اجتماعی‌اِ قطع‌ارتباط شد."

#: templates/socialaccount/signup.html:12
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"چند قدمی ورود به %(site_name)s با حساب‌ات %(provider_name)s هستی. در گام آخر، "
"لطفا فرم زیر را کامل کن:"

#: templates/socialaccount/snippets/login.html:10
msgid "Or use a third-party"
msgstr ""

#: templates/usersessions/messages/sessions_logged_out.txt:2
msgid "Signed out of all other sessions."
msgstr ""

#: templates/usersessions/usersession_list.html:23
msgid "Started At"
msgstr ""

#: templates/usersessions/usersession_list.html:24
#, fuzzy
#| msgid "Email Addresses"
msgid "IP Address"
msgstr "نشانی‌های رایانامه"

#: templates/usersessions/usersession_list.html:25
msgid "Browser"
msgstr ""

#: templates/usersessions/usersession_list.html:27
msgid "Last seen at"
msgstr ""

#: templates/usersessions/usersession_list.html:47
#, fuzzy
#| msgid "Current Password"
msgid "Current"
msgstr "گذرواژه کنونی"

#: templates/usersessions/usersession_list.html:60
msgid "Sign Out Other Sessions"
msgstr ""

#: usersessions/apps.py:9
msgid "User Sessions"
msgstr ""

#: usersessions/models.py:92
msgid "session key"
msgstr ""

#, fuzzy
#~| msgid "Account Connections"
#~ msgid "Account Connection"
#~ msgstr "اتصال‌های حساب"

#, python-brace-format
#~ msgid "Password must be a minimum of {0} characters."
#~ msgstr "گذرواژه باید حداقل {0} کاراکتر باشد."

#, fuzzy, python-format
#~ msgid ""
#~ "You are receiving this email because you or someone else has requested a\n"
#~ "password for your user account. However, we do not have any record of a "
#~ "user\n"
#~ "with email %(email)s in our database.\n"
#~ "\n"
#~ "This mail can be safely ignored if you did not request a password reset.\n"
#~ "\n"
#~ "If it was you, you can sign up for an account using the link below."
#~ msgstr ""
#~ "سلام،\n"
#~ "\n"
#~ "این رایانامه را دریافت کرده‌ای چون برای حساب کاربری‌ات در %(site_name)s،  "
#~ "از جانب خودت یا کسی دیگر، یه گذرواژه درخواست شده.\n"
#~ "برای بازنشانی گذرواژه پیوند زیر را دنبال کن. وگرنه چشم‌پوشی از این هنگامی "
#~ "که خودت هم درخواست نکردی می‌تواند امن باشد."

#, fuzzy
#~| msgid "The following email addresses are associated with your account:"
#~ msgid "The following email address is associated with your account:"
#~ msgstr "نشانی‌های رایانامه زیر به حساب‌ات متصل شده‌اند:"

#, fuzzy
#~| msgid "Confirm Email Address"
#~ msgid "Change Email Address"
#~ msgstr "تایید نشانی رایانامه"

#, python-format
#~ msgid ""
#~ "Please sign in with one\n"
#~ "of your existing third party accounts. Or, <a "
#~ "href=\"%(signup_url)s\">sign up</a>\n"
#~ "for a %(site_name)s account and sign in below:"
#~ msgstr ""
#~ "لطفا با یکی از حساب‌های شخص سوم موجودت وارد شو. یا در %(site_name)s<a "
#~ "href=\"%(signup_url)s\">ثبت‌نام</a> کن و از زیر وارد شو:"

#~ msgid "or"
#~ msgstr "یا"

#~ msgid "change password"
#~ msgstr "تغییر گذرواژه"

#~ msgid "OpenID Sign In"
#~ msgstr "ورودبا OpenID"

#~ msgid "This email address is already associated with another account."
#~ msgstr "این نشانی رایانامه ازقبل به حساب دیگری وصل شده."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "بهت یه رایانامه فرستادیم. اگر تا چند دقیقه‌ی دیگر دریافتش نکردی باهامون "
#~ "تماس بگیر."

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "اطلاعات داده شده درست نیست."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "نام‌کاربری تنها می‌تواند شامل حروف، اعداد، و  @/./+/-/_. باشد"

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "این نام‌کاربری قبلا گرفته شده. لطفا یکی دیگر انتخاب کن."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "ورود با Shopify"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "تایید کرده‌ای که <a href=\"mailto:%(email)s\">%(email)s</a> یه نشانی "
#~ "رایانامه برای کاربر %(user_display)s است."
